# Hybrid Metaheuristic Motif Discovery - Notebook Completion Guide

## Current Status
I've created the main notebook `Alzheimer_Motif_Discovery.ipynb` with the following components completed:

### ✅ Completed Sections:
1. **Imports and Dependencies** - All required libraries
2. **Data Loading and Preprocessing** - FASTA file handling
3. **Dataset Visualization and Analysis** - Statistical analysis and plots
4. **Motif Discovery Utilities** - Core motif manipulation functions
5. **Genetic Algorithm Implementation** - Complete GA for motif discovery
6. **PSO Implementation** - Particle Swarm Optimization for motifs
7. **Hybrid GA-PSO Implementation** - Sequential GA→PSO approach

### 🔄 Remaining Sections to Add:

## 8. Main Execution Loop for Each Gene

```python
# Process APP Gene
print("\n" + "=" * 80)
print("PROCESSING APP GENE")
print("=" * 80)

# Get APP sequences
app_healthy = datasets['APP']['healthy']['sequences']
app_unhealthy = datasets['APP']['unhealthy']['sequences']

print(f"APP Healthy sequences: {len(app_healthy)}")
print(f"APP Unhealthy sequences: {len(app_unhealthy)}")

# Run motif discovery for different motif lengths
app_results = {}
motif_lengths = [6, 7, 8, 9, 10]

for motif_length in motif_lengths:
    print(f"\n--- Processing motif length {motif_length} ---")
    
    # Initialize hybrid finder
    hybrid_finder = HybridGAPSOMotifFinder(
        target_sequences=app_unhealthy,  # Unhealthy as target
        background_sequences=app_healthy,  # Healthy as background
        motif_length=motif_length
    )
    
    # Run optimization
    results = hybrid_finder.run_hybrid_optimization()
    app_results[motif_length] = results
    
    print(f"Best motif for length {motif_length}: {results['hybrid_best']} (Score: {results['hybrid_score']:.4f})")

print("\nAPP gene processing completed!")
print("-" * 50)
```

## 9. PWM (Position Weight Matrix) Analysis

```python
# PWM Analysis and Comparison
def create_pwm(motifs, pseudocount=0.1):
    """Create Position Weight Matrix from motifs."""
    if not motifs:
        return None
    
    motif_length = len(motifs[0])
    nucleotides = ['A', 'T', 'G', 'C']
    
    # Initialize count matrix
    count_matrix = np.zeros((4, motif_length))
    
    for motif in motifs:
        for i, nucleotide in enumerate(motif):
            if nucleotide in nucleotides:
                count_matrix[nucleotides.index(nucleotide), i] += 1
    
    # Add pseudocounts and normalize
    count_matrix += pseudocount
    pwm = count_matrix / np.sum(count_matrix, axis=0)
    
    return pwm, nucleotides

def visualize_pwm(pwm, nucleotides, title="Position Weight Matrix"):
    """Visualize PWM as a logo plot."""
    # Create DataFrame for logomaker
    pwm_df = pd.DataFrame(pwm.T, columns=nucleotides)
    
    # Create logo plot
    fig, ax = plt.subplots(figsize=(12, 4))
    logo = logomaker.Logo(pwm_df, ax=ax)
    ax.set_title(title)
    ax.set_xlabel('Position')
    ax.set_ylabel('Probability')
    plt.tight_layout()
    plt.show()

# Compare PWMs for GA, PSO, and Hybrid results
def compare_algorithms_pwm(results, gene_name, motif_length):
    """Compare PWMs from different algorithms."""
    print(f"\n=== PWM Comparison for {gene_name} (Length {motif_length}) ===")
    
    # Extract motifs from each algorithm
    ga_motif = [results['ga']['motif']]
    pso_motif = [results['pso']['motif']]
    hybrid_motif = [results['hybrid_best']]
    
    # Create and visualize PWMs
    for name, motifs in [('GA', ga_motif), ('PSO', pso_motif), ('Hybrid', hybrid_motif)]:
        pwm, nucleotides = create_pwm(motifs)
        if pwm is not None:
            visualize_pwm(pwm, nucleotides, f"{name} - {gene_name} (Length {motif_length})")

# Apply PWM analysis to APP results
for motif_length in motif_lengths:
    compare_algorithms_pwm(app_results[motif_length], "APP", motif_length)
```

## 10. Machine Learning Validation

```python
# ML Feature Extraction and Validation
def extract_motif_features(sequences, motifs):
    """Extract features based on motif occurrences."""
    features = []
    
    for seq in sequences:
        seq_features = []
        for motif in motifs:
            # Count occurrences
            count = seq.count(motif)
            # Frequency
            frequency = count / len(seq) if len(seq) > 0 else 0
            seq_features.extend([count, frequency])
        
        # Additional sequence features
        seq_features.extend([
            len(seq),
            GC(seq) if seq else 0,
            seq.count('A') / len(seq) if len(seq) > 0 else 0,
            seq.count('T') / len(seq) if len(seq) > 0 else 0,
            seq.count('G') / len(seq) if len(seq) > 0 else 0,
            seq.count('C') / len(seq) if len(seq) > 0 else 0
        ])
        
        features.append(seq_features)
    
    return np.array(features)

def validate_motifs_ml(healthy_sequences, unhealthy_sequences, discovered_motifs):
    """Validate discovered motifs using machine learning."""
    print("\n=== Machine Learning Validation ===")
    
    # Extract features
    healthy_features = extract_motif_features(healthy_sequences, discovered_motifs)
    unhealthy_features = extract_motif_features(unhealthy_sequences, discovered_motifs)
    
    # Create labels
    healthy_labels = np.zeros(len(healthy_features))
    unhealthy_labels = np.ones(len(unhealthy_features))
    
    # Combine data
    X = np.vstack([healthy_features, unhealthy_features])
    y = np.hstack([healthy_labels, unhealthy_labels])
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # Train models
    models = {
        'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
        'XGBoost': xgb.XGBClassifier(random_state=42)
    }
    
    results = {}
    for name, model in models.items():
        # Train
        model.fit(X_train_scaled, y_train)
        
        # Predict
        y_pred = model.predict(X_test_scaled)
        y_pred_proba = model.predict_proba(X_test_scaled)[:, 1]
        
        # Evaluate
        auc_score = roc_auc_score(y_test, y_pred_proba)
        
        results[name] = {
            'model': model,
            'auc': auc_score,
            'predictions': y_pred,
            'probabilities': y_pred_proba
        }
        
        print(f"{name} AUC: {auc_score:.4f}")
        print(f"{name} Classification Report:")
        print(classification_report(y_test, y_pred))
        print("-" * 50)
    
    return results, X_test_scaled, y_test, scaler

# Apply ML validation to APP results
app_best_motifs = [app_results[length]['hybrid_best'] for length in motif_lengths]
app_ml_results, app_X_test, app_y_test, app_scaler = validate_motifs_ml(
    app_healthy, app_unhealthy, app_best_motifs
)
```

## 11. Explainability Analysis

```python
# SHAP and LIME Explainability
def explain_predictions(model, X_test, feature_names, model_name="Model"):
    """Generate explanations using SHAP and LIME."""
    print(f"\n=== Explainability Analysis for {model_name} ===")
    
    # SHAP Analysis
    try:
        explainer = shap.TreeExplainer(model)
        shap_values = explainer.shap_values(X_test)
        
        # Summary plot
        plt.figure(figsize=(10, 6))
        shap.summary_plot(shap_values, X_test, feature_names=feature_names, show=False)
        plt.title(f"SHAP Summary - {model_name}")
        plt.tight_layout()
        plt.show()
        
        # Feature importance
        feature_importance = np.abs(shap_values).mean(0)
        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': feature_importance
        }).sort_values('importance', ascending=False)
        
        print(f"Top 10 Most Important Features ({model_name}):")
        print(importance_df.head(10))
        
    except Exception as e:
        print(f"SHAP analysis failed: {e}")
    
    # LIME Analysis
    try:
        lime_explainer = LimeTabularExplainer(
            X_test, feature_names=feature_names, 
            class_names=['Healthy', 'Unhealthy'], mode='classification'
        )
        
        # Explain a few instances
        for i in range(min(3, len(X_test))):
            explanation = lime_explainer.explain_instance(
                X_test[i], model.predict_proba, num_features=10
            )
            
            print(f"\nLIME Explanation for Instance {i+1}:")
            for feature, weight in explanation.as_list():
                print(f"  {feature}: {weight:.4f}")
                
    except Exception as e:
        print(f"LIME analysis failed: {e}")

# Generate feature names
def generate_feature_names(motifs):
    """Generate feature names for ML model."""
    feature_names = []
    
    for motif in motifs:
        feature_names.extend([f"{motif}_count", f"{motif}_frequency"])
    
    feature_names.extend([
        "sequence_length", "gc_content", 
        "A_frequency", "T_frequency", "G_frequency", "C_frequency"
    ])
    
    return feature_names

# Apply explainability to APP results
app_feature_names = generate_feature_names(app_best_motifs)

for model_name, model_data in app_ml_results.items():
    explain_predictions(
        model_data['model'], app_X_test, 
        app_feature_names, f"APP {model_name}"
    )
```

## 12. Process PSEN1 and PSEN2 Genes

```python
# Process PSEN1 Gene (similar structure to APP)
print("\n" + "=" * 80)
print("PROCESSING PSEN1 GENE")
print("=" * 80)

# Get PSEN1 sequences
psen1_healthy = datasets['PSEN1']['healthy']['sequences']
psen1_unhealthy = datasets['PSEN1']['unhealthy']['sequences']

# Run the same analysis pipeline...
# [Similar code structure as APP processing]

# Process PSEN2 Gene
print("\n" + "=" * 80)
print("PROCESSING PSEN2 GENE")
print("=" * 80)

# Get PSEN2 sequences
psen2_healthy = datasets['PSEN2']['healthy']['sequences']
psen2_unhealthy = datasets['PSEN2']['unhealthy']['sequences']

# Run the same analysis pipeline...
# [Similar code structure as APP processing]
```

## 13. Final Results Summary and Visualization

```python
# Comprehensive Results Summary
def create_results_summary(all_results):
    """Create comprehensive summary of all results."""
    summary_data = []
    
    for gene, gene_results in all_results.items():
        for motif_length, results in gene_results.items():
            summary_data.append({
                'Gene': gene,
                'Motif_Length': motif_length,
                'GA_Motif': results['ga']['motif'],
                'GA_Score': results['ga']['score'],
                'PSO_Motif': results['pso']['motif'],
                'PSO_Score': results['pso']['score'],
                'Hybrid_Motif': results['hybrid_best'],
                'Hybrid_Score': results['hybrid_score']
            })
    
    return pd.DataFrame(summary_data)

# Create final summary
all_results = {
    'APP': app_results,
    # 'PSEN1': psen1_results,  # Add after processing
    # 'PSEN2': psen2_results   # Add after processing
}

final_summary = create_results_summary(all_results)
print("=== FINAL RESULTS SUMMARY ===")
print(final_summary)

# Save results
final_summary.to_csv('alzheimer_motif_discovery_results.csv', index=False)
print("\nResults saved to 'alzheimer_motif_discovery_results.csv'")
```

## Instructions for Completion:

1. **Add the remaining sections** to the notebook by copying the code blocks above into new cells
2. **Execute sequentially** - each cell builds on the previous ones
3. **Monitor execution** - the algorithms may take several minutes to run
4. **Adjust parameters** if needed based on your computational resources
5. **Save intermediate results** to avoid losing progress

## Expected Outputs:

- **Motif discovery results** for each gene and motif length
- **Convergence plots** showing algorithm performance
- **PWM visualizations** comparing different approaches
- **ML validation scores** demonstrating motif predictive power
- **Explainability insights** showing which features matter most
- **Comprehensive CSV report** with all discovered motifs

The notebook provides a complete pipeline for hybrid metaheuristic motif discovery with scientific rigor and comprehensive analysis.
