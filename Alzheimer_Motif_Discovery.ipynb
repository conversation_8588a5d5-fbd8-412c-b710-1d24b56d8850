# Install required packages
!pip install biopython scikit-learn pandas numpy matplotlib seaborn
!pip install deap pyswarm logomaker
!pip install shap lime xgboost
!pip install plotly kaleido

# Alternative installation if above fails
# !pip install biopython==1.79
# !pip install deap==1.3.3
# !conda install -c conda-forge logomaker -y

# Core libraries
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter, defaultdict
import random
import warnings
warnings.filterwarnings('ignore')

# Bioinformatics
from Bio import SeqIO
from Bio.Seq import Seq
try:
    from Bio.SeqUtils import GC
except ImportError:
    # Alternative GC content calculation if import fails
    def GC(seq):
        if not seq:
            return 0
        gc_count = seq.count('G') + seq.count('C')
        return (gc_count / len(seq)) * 100

# Metaheuristics
from deap import base, creator, tools, algorithms
import pyswarm

# Machine Learning
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
from sklearn.preprocessing import StandardScaler
import xgboost as xgb

# Explainability
import shap
import lime
from lime.lime_tabular import LimeTabularExplainer

# Visualization
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import logomaker

# Set random seeds for reproducibility
np.random.seed(42)
random.seed(42)

print("All dependencies imported successfully!")

# Upload FASTA files
from google.colab import files

print("Please upload your 6 FASTA files:")
print("1. APP_healthy_combined.fasta")
print("2. app_unhealthy_combined.fasta")
print("3. psen1_healthy_combined.fasta")
print("4. psen1_unhealthy_combined.fasta")
print("5. psen2_healthy_combined.fasta")
print("6. psen2_unhealthy_combined.fasta")

uploaded = files.upload()
print(f"\nUploaded {len(uploaded)} files successfully!")

def load_fasta_sequences(filename):
    """
    Load sequences from FASTA file, preserving headers and sequences as they are.
    """
    sequences = []
    headers = []
    
    try:
        for record in SeqIO.parse(filename, "fasta"):
            headers.append(record.description)
            sequences.append(str(record.seq).upper())
        
        print(f"Loaded {len(sequences)} sequences from {filename}")
        if sequences:
            print(f"First sequence length: {len(sequences[0])}")
            print(f"First header: {headers[0][:100]}...")
        
        return sequences, headers
    except Exception as e:
        print(f"Error loading {filename}: {e}")
        return [], []

# Load all datasets
datasets = {}
gene_files = {
    'APP': {
        'healthy': 'APP_healthy_combined.fasta',
        'unhealthy': 'app_unhealthy_combined.fasta'
    },
    'PSEN1': {
        'healthy': 'psen1_healthy_combined.fasta',
        'unhealthy': 'psen1_unhealthy_combined.fasta'
    },
    'PSEN2': {
        'healthy': 'psen2_healthy_combined.fasta',
        'unhealthy': 'psen2_unhealthy_combined.fasta'
    }
}

for gene in gene_files:
    datasets[gene] = {}
    for condition in ['healthy', 'unhealthy']:
        filename = gene_files[gene][condition]
        sequences, headers = load_fasta_sequences(filename)
        datasets[gene][condition] = {
            'sequences': sequences,
            'headers': headers
        }
    print(f"\n{gene} gene loaded successfully!")
    print("-" * 50)

def analyze_sequences(sequences, gene_name, condition):
    """
    Analyze basic statistics of sequences.
    """
    if not sequences:
        return {}
    
    lengths = [len(seq) for seq in sequences]
    gc_contents = [GC(seq) for seq in sequences if seq]
    
    # Nucleotide composition
    all_nucleotides = ''.join(sequences)
    nucleotide_counts = Counter(all_nucleotides)
    total_nucleotides = sum(nucleotide_counts.values())
    
    stats = {
        'gene': gene_name,
        'condition': condition,
        'num_sequences': len(sequences),
        'total_length': sum(lengths),
        'avg_length': np.mean(lengths),
        'min_length': min(lengths),
        'max_length': max(lengths),
        'avg_gc_content': np.mean(gc_contents) if gc_contents else 0,
        'nucleotide_composition': {
            nt: count/total_nucleotides*100 for nt, count in nucleotide_counts.items()
        }
    }
    
    return stats

# Analyze all datasets
all_stats = []
for gene in datasets:
    for condition in ['healthy', 'unhealthy']:
        sequences = datasets[gene][condition]['sequences']
        stats = analyze_sequences(sequences, gene, condition)
        if stats:
            all_stats.append(stats)

# Create summary DataFrame
summary_df = pd.DataFrame(all_stats)
print("Dataset Summary:")
print(summary_df[['gene', 'condition', 'num_sequences', 'total_length', 'avg_length', 'avg_gc_content']].round(2))

# Visualization of dataset characteristics
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# 1. Sequence counts
genes = summary_df['gene'].unique()
healthy_counts = [summary_df[(summary_df['gene']==g) & (summary_df['condition']=='healthy')]['num_sequences'].iloc[0] for g in genes]
unhealthy_counts = [summary_df[(summary_df['gene']==g) & (summary_df['condition']=='unhealthy')]['num_sequences'].iloc[0] for g in genes]

x = np.arange(len(genes))
width = 0.35

axes[0,0].bar(x - width/2, healthy_counts, width, label='Healthy', color='lightblue')
axes[0,0].bar(x + width/2, unhealthy_counts, width, label='Unhealthy', color='lightcoral')
axes[0,0].set_xlabel('Genes')
axes[0,0].set_ylabel('Number of Sequences')
axes[0,0].set_title('Sequence Counts by Gene and Condition')
axes[0,0].set_xticks(x)
axes[0,0].set_xticklabels(genes)
axes[0,0].legend()

# 2. Average sequence lengths
healthy_lengths = [summary_df[(summary_df['gene']==g) & (summary_df['condition']=='healthy')]['avg_length'].iloc[0] for g in genes]
unhealthy_lengths = [summary_df[(summary_df['gene']==g) & (summary_df['condition']=='unhealthy')]['avg_length'].iloc[0] for g in genes]

axes[0,1].bar(x - width/2, healthy_lengths, width, label='Healthy', color='lightblue')
axes[0,1].bar(x + width/2, unhealthy_lengths, width, label='Unhealthy', color='lightcoral')
axes[0,1].set_xlabel('Genes')
axes[0,1].set_ylabel('Average Sequence Length')
axes[0,1].set_title('Average Sequence Lengths')
axes[0,1].set_xticks(x)
axes[0,1].set_xticklabels(genes)
axes[0,1].legend()

# 3. GC Content
healthy_gc = [summary_df[(summary_df['gene']==g) & (summary_df['condition']=='healthy')]['avg_gc_content'].iloc[0] for g in genes]
unhealthy_gc = [summary_df[(summary_df['gene']==g) & (summary_df['condition']=='unhealthy')]['avg_gc_content'].iloc[0] for g in genes]

axes[1,0].bar(x - width/2, healthy_gc, width, label='Healthy', color='lightblue')
axes[1,0].bar(x + width/2, unhealthy_gc, width, label='Unhealthy', color='lightcoral')
axes[1,0].set_xlabel('Genes')
axes[1,0].set_ylabel('Average GC Content (%)')
axes[1,0].set_title('GC Content Comparison')
axes[1,0].set_xticks(x)
axes[1,0].set_xticklabels(genes)
axes[1,0].legend()

# 4. Nucleotide composition for first gene (APP)
app_healthy_comp = summary_df[(summary_df['gene']=='APP') & (summary_df['condition']=='healthy')]['nucleotide_composition'].iloc[0]
nucleotides = list(app_healthy_comp.keys())
compositions = list(app_healthy_comp.values())

axes[1,1].pie(compositions, labels=nucleotides, autopct='%1.1f%%', startangle=90)
axes[1,1].set_title('Nucleotide Composition (APP Healthy)')

plt.tight_layout()
plt.show()

print("\nDataset visualization completed!")

# Motif discovery utility functions\n
class MotifDiscovery:\n
    def __init__(self, sequences, motif_length_range=(6, 10)):\n
        self.sequences = sequences\n
        self.motif_length_range = motif_length_range\n
        self.nucleotides = ['A', 'T', 'G', 'C']\n
        \n
    def generate_random_motif(self, length):\n
        \"\"\"Generate a random motif of given length.\"\"\"\n
        if length <= 0:\n
            return ''\n
        return ''.join(random.choices(self.nucleotides, k=length))\n
    \n
    def count_motif_occurrences(self, motif, sequences):\n
        \"\"\"Count occurrences of motif in sequences.\"\"\"\n
        if not motif or not sequences:\n
            return 0\n
        \n
        count = 0\n
        for seq in sequences:\n
            if seq and len(seq) >= len(motif):\n
                count += seq.upper().count(motif.upper())\n
        return count\n
    \n
    def calculate_motif_score(self, motif, target_sequences, background_sequences):\n
        \"\"\"Calculate motif score based on enrichment in target vs background.\"\"\"\n
        # Input validation\n
        if not motif or not target_sequences or not background_sequences:\n
            return 0.0\n
        \n
        # Filter out empty sequences\n
        target_seqs = [seq for seq in target_sequences if seq and len(seq) > 0]\n
        background_seqs = [seq for seq in background_sequences if seq and len(seq) > 0]\n
        \n
        if not target_seqs or not background_seqs:\n
            return 0.0\n
        \n
        target_count = self.count_motif_occurrences(motif, target_seqs)\n
        background_count = self.count_motif_occurrences(motif, background_seqs)\n
        \n
        # Calculate total sequence lengths\n
        target_total_length = sum(len(seq) for seq in target_seqs)\n
        background_total_length = sum(len(seq) for seq in background_seqs)\n
        \n
        # Avoid division by zero\n
        if target_total_length == 0 or background_total_length == 0:\n
            return 0.0\n
        \n
        # Calculate frequencies\n
        target_freq = target_count / target_total_length\n
        background_freq = background_count / background_total_length\n
        \n
        # Calculate enrichment score\n
        if background_freq == 0:\n
            if target_freq > 0:\n
                enrichment = target_freq * 1000  # High score for unique motifs\n
            else:\n
                enrichment = 0.0\n
        else:\n
            enrichment = target_freq / background_freq\n
        \n
        # Final score considers both enrichment and absolute frequency\n
        score = enrichment * target_freq * 1000  # Scale for better numeric range\n
        return max(0.0, score)  # Ensure non-negative score\n
    \n
    def mutate_motif(self, motif, mutation_rate=0.1):\n
        \"\"\"Mutate a motif by changing some nucleotides.\"\"\"\n
        if not motif or mutation_rate <= 0:\n
            return motif\n
        \n
        motif_list = list(motif.upper())\n
        for i in range(len(motif_list)):\n
            if random.random() < mutation_rate:\n
                # Choose a different nucleotide\n
                available_nucleotides = [n for n in self.nucleotides if n != motif_list[i]]\n
                if available_nucleotides:\n
                    motif_list[i] = random.choice(available_nucleotides)\n
        return ''.join(motif_list)\n
    \n
    def crossover_motifs(self, motif1, motif2):\n
        \"\"\"Perform crossover between two motifs.\"\"\"\n
        if not motif1 or not motif2 or len(motif1) != len(motif2) or len(motif1) < 2:\n
            return motif1, motif2\n
        \n
        motif1 = motif1.upper()\n
        motif2 = motif2.upper()\n
        \n
        crossover_point = random.randint(1, len(motif1) - 1)\n
        child1 = motif1[:crossover_point] + motif2[crossover_point:]\n
        child2 = motif2[:crossover_point] + motif1[crossover_point:]\n
        return child1, child2\n
\n
print(\"Motif discovery utilities defined!\")

# Genetic Algorithm Implementation\n
class GeneticAlgorithmMotifFinder:\n
    def __init__(self, target_sequences, background_sequences, \n
                 population_size=100, generations=50, motif_length=8):\n
        self.target_sequences = target_sequences\n
        self.background_sequences = background_sequences\n
        self.population_size = population_size\n
        self.generations = generations\n
        self.motif_length = motif_length\n
        self.motif_discovery = MotifDiscovery(target_sequences)\n
        self.best_scores = []\n
        self.avg_scores = []\n
        \n
    def initialize_population(self):\n
        \"\"\"Initialize random population of motifs.\"\"\"\n
        return [self.motif_discovery.generate_random_motif(self.motif_length) \n
                for _ in range(self.population_size)]\n
    \n
    def evaluate_fitness(self, motif):\n
        \"\"\"Evaluate fitness of a motif.\"\"\"\n
        return self.motif_discovery.calculate_motif_score(\n
            motif, self.target_sequences, self.background_sequences)\n
    \n
    def selection(self, population, fitness_scores, k=3):\n
        \"\"\"Tournament selection.\"\"\"\n
        selected = []\n
        for _ in range(len(population)):\n
            tournament_indices = random.sample(range(len(population)), k)\n
            tournament_fitness = [fitness_scores[i] for i in tournament_indices]\n
            winner_index = tournament_indices[np.argmax(tournament_fitness)]\n
            selected.append(population[winner_index])\n
        return selected\n
    \n
    def evolve(self):\n
        \"\"\"Run the genetic algorithm.\"\"\"\n
        population = self.initialize_population()\n
        \n
        for generation in range(self.generations):\n
            # Evaluate fitness\n
            fitness_scores = [self.evaluate_fitness(motif) for motif in population]\n
            \n
            # Track statistics\n
            self.best_scores.append(max(fitness_scores))\n
            self.avg_scores.append(np.mean(fitness_scores))\n
            \n
            # Selection\n
            selected = self.selection(population, fitness_scores)\n
            \n
            # Crossover and mutation\n
            new_population = []\n
            for i in range(0, len(selected), 2):\n
                parent1 = selected[i]\n
                parent2 = selected[i + 1] if i + 1 < len(selected) else selected[0]\n
                \n
                # Crossover\n
                if random.random() < 0.8:  # Crossover probability\n
                    child1, child2 = self.motif_discovery.crossover_motifs(parent1, parent2)\n
                else:\n
                    child1, child2 = parent1, parent2\n
                \n
                # Mutation\n
                child1 = self.motif_discovery.mutate_motif(child1, 0.1)\n
                child2 = self.motif_discovery.mutate_motif(child2, 0.1)\n
                \n
                new_population.extend([child1, child2])\n
            \n
            population = new_population[:self.population_size]\n
            \n
            if generation % 10 == 0:\n
                print(f\"Generation {generation}: Best score = {max(fitness_scores):.4f}\")\n
        \n
        # Return best motif\n
        final_fitness = [self.evaluate_fitness(motif) for motif in population]\n
        best_index = np.argmax(final_fitness)\n
        best_motif = population[best_index]\n
        best_score = final_fitness[best_index]\n
        \n
        return best_motif, best_score, population\n
\n
print(\"Genetic Algorithm implementation completed!\")

# Particle Swarm Optimization Implementation\n
class PSOMotifFinder:\n
    def __init__(self, target_sequences, background_sequences, \n
                 swarm_size=50, iterations=100, motif_length=8):\n
        self.target_sequences = target_sequences\n
        self.background_sequences = background_sequences\n
        self.swarm_size = swarm_size\n
        self.iterations = iterations\n
        self.motif_length = motif_length\n
        self.motif_discovery = MotifDiscovery(target_sequences)\n
        self.nucleotides = ['A', 'T', 'G', 'C']\n
        self.best_scores = []\n
        self.avg_scores = []\n
        \n
    def motif_to_vector(self, motif):\n
        \"\"\"Convert motif string to numerical vector.\"\"\"\n
        vector = []\n
        for nucleotide in motif:\n
            if nucleotide == 'A':\n
                vector.extend([1, 0, 0, 0])\n
            elif nucleotide == 'T':\n
                vector.extend([0, 1, 0, 0])\n
            elif nucleotide == 'G':\n
                vector.extend([0, 0, 1, 0])\n
            elif nucleotide == 'C':\n
                vector.extend([0, 0, 0, 1])\n
        return np.array(vector)\n
    \n
    def vector_to_motif(self, vector):\n
        \"\"\"Convert numerical vector to motif string.\"\"\"\n
        motif = ''\n
        for i in range(0, len(vector), 4):\n
            nucleotide_probs = vector[i:i+4]\n
            # Apply softmax to get probabilities\n
            exp_probs = np.exp(nucleotide_probs - np.max(nucleotide_probs))\n
            probs = exp_probs / np.sum(exp_probs)\n
            # Choose nucleotide based on highest probability\n
            chosen_idx = np.argmax(probs)\n
            motif += self.nucleotides[chosen_idx]\n
        return motif\n
    \n
    def fitness_function(self, vector):\n
        \"\"\"Fitness function for PSO.\"\"\"\n
        motif = self.vector_to_motif(vector)\n
        return self.motif_discovery.calculate_motif_score(\n
            motif, self.target_sequences, self.background_sequences)\n
    \n
    def optimize(self, initial_motifs=None):\n
        \"\"\"Run PSO optimization.\"\"\"\n
        # Initialize swarm\n
        if initial_motifs:\n
            # Use provided initial motifs (e.g., from GA)\n
            particles = []\n
            for motif in initial_motifs[:self.swarm_size]:\n
                particles.append(self.motif_to_vector(motif))\n
            # Fill remaining particles randomly if needed\n
            while len(particles) < self.swarm_size:\n
                random_motif = self.motif_discovery.generate_random_motif(self.motif_length)\n
                particles.append(self.motif_to_vector(random_motif))\n
        else:\n
            # Random initialization\n
            particles = []\n
            for _ in range(self.swarm_size):\n
                random_motif = self.motif_discovery.generate_random_motif(self.motif_length)\n
                particles.append(self.motif_to_vector(random_motif))\n
        \n
        particles = np.array(particles)\n
        velocities = np.random.uniform(-1, 1, particles.shape)\n
        \n
        # Initialize personal and global bests\n
        personal_best_positions = particles.copy()\n
        personal_best_scores = np.array([self.fitness_function(p) for p in particles])\n
        \n
        global_best_idx = np.argmax(personal_best_scores)\n
        global_best_position = personal_best_positions[global_best_idx].copy()\n
        global_best_score = personal_best_scores[global_best_idx]\n
        \n
        # PSO parameters\n
        w = 0.7  # Inertia weight\n
        c1 = 1.5  # Cognitive parameter\n
        c2 = 1.5  # Social parameter\n
        \n
        for iteration in range(self.iterations):\n
            for i in range(self.swarm_size):\n
                # Update velocity\n
                r1, r2 = np.random.random(2)\n
                velocities[i] = (w * velocities[i] + \n
                               c1 * r1 * (personal_best_positions[i] - particles[i]) +\n
                               c2 * r2 * (global_best_position - particles[i]))\n
                \n
                # Update position\n
                particles[i] += velocities[i]\n
                \n
                # Evaluate fitness\n
                fitness = self.fitness_function(particles[i])\n
                \n
                # Update personal best\n
                if fitness > personal_best_scores[i]:\n
                    personal_best_scores[i] = fitness\n
                    personal_best_positions[i] = particles[i].copy()\n
                \n
                # Update global best\n
                if fitness > global_best_score:\n
                    global_best_score = fitness\n
                    global_best_position = particles[i].copy()\n
            \n
            # Track statistics\n
            current_scores = [self.fitness_function(p) for p in particles]\n
            self.best_scores.append(max(current_scores))\n
            self.avg_scores.append(np.mean(current_scores))\n
            \n
            if iteration % 20 == 0:\n
                print(f\"Iteration {iteration}: Best score = {global_best_score:.4f}\")\n
        \n
        best_motif = self.vector_to_motif(global_best_position)\n
        return best_motif, global_best_score, [self.vector_to_motif(p) for p in particles]\n
\n
print(\"PSO implementation completed!\")

# Hybrid GA-PSO Implementation\n
class HybridGAPSOMotifFinder:\n
    def __init__(self, target_sequences, background_sequences, motif_length=8):\n
        self.target_sequences = target_sequences\n
        self.background_sequences = background_sequences\n
        self.motif_length = motif_length\n
        self.motif_discovery = MotifDiscovery(target_sequences)\n
        \n
    def run_hybrid_optimization(self):\n
        \"\"\"Run hybrid GA-PSO optimization.\"\"\"\n
        print(\"\n=== Running Hybrid GA-PSO Optimization ===\")\n
        \n
        # Step 1: Run GA for exploration\n
        print(\"Step 1: Running Genetic Algorithm for exploration...\")\n
        ga = GeneticAlgorithmMotifFinder(\n
            self.target_sequences, self.background_sequences,\n
            population_size=100, generations=30, motif_length=self.motif_length\n
        )\n
        ga_best_motif, ga_best_score, ga_population = ga.evolve()\n
        \n
        print(f\"GA Results: Best motif = {ga_best_motif}, Score = {ga_best_score:.4f}\")\n
        \n
        # Step 2: Use GA results as initial population for PSO\n
        print(\"\nStep 2: Running PSO with GA results as initial population...\")\n
        \n
        # Extract motifs from GA population\n
        ga_motifs = [''.join(individual) for individual in ga_population]\n
        \n
        pso = PSOMotifFinder(\n
            self.target_sequences, self.background_sequences,\n
            swarm_size=50, iterations=50, motif_length=self.motif_length\n
        )\n
        pso_best_motif, pso_best_score, pso_population = pso.optimize(ga_motifs)\n
        \n
        print(f\"PSO Results: Best motif = {pso_best_motif}, Score = {pso_best_score:.4f}\")\n
        \n
        # Return results from both algorithms\n
        return {\n
            'ga': {'motif': ga_best_motif, 'score': ga_best_score, 'convergence': ga.best_scores},\n
            'pso': {'motif': pso_best_motif, 'score': pso_best_score, 'convergence': pso.best_scores},\n
            'hybrid_best': pso_best_motif if pso_best_score > ga_best_score else ga_best_motif,\n
            'hybrid_score': max(pso_best_score, ga_best_score)\n
        }\n
\n
print(\"Hybrid GA-PSO implementation completed!\")

# Test the implementation with a small example\n
print('Testing implementation...')\n
\n
# Check if datasets are loaded\n
if 'datasets' in globals():\n
    print('Datasets loaded successfully')\n
    for gene in ['APP', 'PSEN1', 'PSEN2']:\n
        if gene in datasets:\n
            healthy_count = len(datasets[gene]['healthy']['sequences'])\n
            unhealthy_count = len(datasets[gene]['unhealthy']['sequences'])\n
            print(f'{gene}: {healthy_count} healthy, {unhealthy_count} unhealthy sequences')\n
        else:\n
            print(f'{gene}: Not found in datasets')\n
else:\n
    print('ERROR: Datasets not loaded. Please run the data loading cell first.')\n
\n
# Test motif discovery classes\n
try:\n
    test_sequences = ['ATCGATCG', 'GCTAGCTA', 'TTAACCGG']\n
    motif_discovery = MotifDiscovery(test_sequences)\n
    test_motif = motif_discovery.generate_random_motif(6)\n
    print(f'Generated test motif: {test_motif}')\n
    print('Motif discovery classes working correctly')\n
except Exception as e:\n
    print(f'Error in motif discovery classes: {e}')\n
\n
print('Implementation test completed!')

# Process APP Gene
print('\n' + '=' * 80)
print('PROCESSING APP GENE')
print('=' * 80)

# Get APP sequences
app_healthy = datasets['APP']['healthy']['sequences']
app_unhealthy = datasets['APP']['unhealthy']['sequences']

print(f'APP Healthy sequences: {len(app_healthy)}')
print(f'APP Unhealthy sequences: {len(app_unhealthy)}')

# Check if sequences exist
if not app_healthy or not app_unhealthy:
    print('ERROR: APP sequences not found. Please check data loading.')
else:
    # Run motif discovery for different motif lengths
    app_results = {}
    motif_lengths = [6, 7, 8, 9, 10]
    
    for motif_length in motif_lengths:
        print(f'\n--- Processing motif length {motif_length} ---')
        
        try:
            # Initialize hybrid finder
            hybrid_finder = HybridGAPSOMotifFinder(
                target_sequences=app_unhealthy,  # Unhealthy as target
                background_sequences=app_healthy,  # Healthy as background
                motif_length=motif_length
            )
            
            # Run optimization
            results = hybrid_finder.run_hybrid_optimization()
            app_results[motif_length] = results
            
            print(f'Best motif for length {motif_length}: {results["hybrid_best"]} (Score: {results["hybrid_score"]:.4f})')
            
        except Exception as e:
            print(f'Error processing motif length {motif_length}: {str(e)}')
            continue
    
    print('\nAPP gene processing completed!')
    print('-' * 50)

# PWM Analysis and Comparison
def create_pwm(motifs, pseudocount=0.1):
    """Create Position Weight Matrix from motifs."""
    if not motifs:
        return None, None
    
    motif_length = len(motifs[0])
    nucleotides = ['A', 'T', 'G', 'C']
    
    # Initialize count matrix
    count_matrix = np.zeros((4, motif_length))
    
    for motif in motifs:
        for i, nucleotide in enumerate(motif):
            if nucleotide in nucleotides:
                count_matrix[nucleotides.index(nucleotide), i] += 1
    
    # Add pseudocounts and normalize
    count_matrix += pseudocount
    pwm = count_matrix / np.sum(count_matrix, axis=0)
    
    return pwm, nucleotides

def visualize_pwm(pwm, nucleotides, title="Position Weight Matrix"):
    """Visualize PWM as a logo plot."""
    # Create DataFrame for logomaker
    pwm_df = pd.DataFrame(pwm.T, columns=nucleotides)
    
    # Create logo plot
    fig, ax = plt.subplots(figsize=(12, 4))
    try:
        logo = logomaker.Logo(pwm_df, ax=ax)
        ax.set_title(title)
        ax.set_xlabel('Position')
        ax.set_ylabel('Probability')
    except:
        # Fallback to heatmap if logomaker fails
        sns.heatmap(pwm, annot=True, xticklabels=range(1, len(pwm[0])+1), 
                   yticklabels=nucleotides, cmap='Blues', ax=ax)
        ax.set_title(title)
        ax.set_xlabel('Position')
        ax.set_ylabel('Nucleotide')
    
    plt.tight_layout()
    plt.show()

# Compare PWMs for GA, PSO, and Hybrid results
def compare_algorithms_pwm(results, gene_name, motif_length):
    """Compare PWMs from different algorithms."""
    print(f"\n=== PWM Comparison for {gene_name} (Length {motif_length}) ===")
    
    # Extract motifs from each algorithm
    ga_motif = [results['ga']['motif']]
    pso_motif = [results['pso']['motif']]
    hybrid_motif = [results['hybrid_best']]
    
    # Create and visualize PWMs
    for name, motifs in [('GA', ga_motif), ('PSO', pso_motif), ('Hybrid', hybrid_motif)]:
        pwm, nucleotides = create_pwm(motifs)
        if pwm is not None:
            visualize_pwm(pwm, nucleotides, f"{name} - {gene_name} (Length {motif_length})")

# Apply PWM analysis to APP results
if 'app_results' in globals() and app_results:
    for motif_length in motif_lengths:
        if motif_length in app_results:
            compare_algorithms_pwm(app_results[motif_length], "APP", motif_length)
else:
    print('APP results not available. Please run the APP processing cell first.')

# ML Feature Extraction and Validation
def extract_motif_features(sequences, motifs):
    """Extract features based on motif occurrences."""
    features = []
    
    for seq in sequences:
        seq_features = []
        for motif in motifs:
            # Count occurrences
            count = seq.count(motif)
            # Frequency
            frequency = count / len(seq) if len(seq) > 0 else 0
            seq_features.extend([count, frequency])
        
        # Additional sequence features
        seq_features.extend([
            len(seq),
            GC(seq) if seq else 0,
            seq.count('A') / len(seq) if len(seq) > 0 else 0,
            seq.count('T') / len(seq) if len(seq) > 0 else 0,
            seq.count('G') / len(seq) if len(seq) > 0 else 0,
            seq.count('C') / len(seq) if len(seq) > 0 else 0
        ])
        
        features.append(seq_features)
    
    return np.array(features)

def validate_motifs_ml(healthy_sequences, unhealthy_sequences, discovered_motifs):
    """Validate discovered motifs using machine learning."""
    print("\n=== Machine Learning Validation ===")
    
    # Extract features
    healthy_features = extract_motif_features(healthy_sequences, discovered_motifs)
    unhealthy_features = extract_motif_features(unhealthy_sequences, discovered_motifs)
    
    # Create labels
    healthy_labels = np.zeros(len(healthy_features))
    unhealthy_labels = np.ones(len(unhealthy_features))
    
    # Combine data
    X = np.vstack([healthy_features, unhealthy_features])
    y = np.hstack([healthy_labels, unhealthy_labels])
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # Train models
    models = {
        'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
        'XGBoost': xgb.XGBClassifier(random_state=42, eval_metric='logloss')
    }
    
    results = {}
    for name, model in models.items():
        # Train
        model.fit(X_train_scaled, y_train)
        
        # Predict
        y_pred = model.predict(X_test_scaled)
        y_pred_proba = model.predict_proba(X_test_scaled)[:, 1]
        
        # Evaluate
        auc_score = roc_auc_score(y_test, y_pred_proba)
        
        results[name] = {
            'model': model,
            'auc': auc_score,
            'predictions': y_pred,
            'probabilities': y_pred_proba
        }
        
        print(f"{name} AUC: {auc_score:.4f}")
        print(f"{name} Classification Report:")
        print(classification_report(y_test, y_pred))
        print("-" * 50)
    
    return results, X_test_scaled, y_test, scaler

# Apply ML validation to APP results
if 'app_results' in globals() and app_results:
    app_best_motifs = [app_results[length]['hybrid_best'] for length in motif_lengths if length in app_results]
    if app_best_motifs:
        app_ml_results, app_X_test, app_y_test, app_scaler = validate_motifs_ml(
            app_healthy, app_unhealthy, app_best_motifs
        )
    else:
        print('No APP motifs found for ML validation.')
else:
    print('APP results not available. Please run the APP processing cell first.')

# SHAP and LIME Explainability
def explain_predictions(model, X_test, feature_names, model_name="Model"):
    """Generate explanations using SHAP and LIME."""
    print(f"\n=== Explainability Analysis for {model_name} ===")
    
    # SHAP Analysis
    try:
        if hasattr(model, 'feature_importances_'):
            # For tree-based models
            explainer = shap.TreeExplainer(model)
            shap_values = explainer.shap_values(X_test)
            
            # Handle binary classification
            if isinstance(shap_values, list):
                shap_values = shap_values[1]  # Use positive class
            
            # Summary plot
            plt.figure(figsize=(10, 6))
            shap.summary_plot(shap_values, X_test, feature_names=feature_names, show=False)
            plt.title(f"SHAP Summary - {model_name}")
            plt.tight_layout()
            plt.show()
            
            # Feature importance
            feature_importance = np.abs(shap_values).mean(0)
            importance_df = pd.DataFrame({
                'feature': feature_names,
                'importance': feature_importance
            }).sort_values('importance', ascending=False)
            
            print(f"Top 10 Most Important Features ({model_name}):")
            print(importance_df.head(10))
            
    except Exception as e:
        print(f"SHAP analysis failed: {e}")
    
    # LIME Analysis
    try:
        lime_explainer = LimeTabularExplainer(
            X_test, feature_names=feature_names, 
            class_names=['Healthy', 'Unhealthy'], mode='classification'
        )
        
        # Explain a few instances
        for i in range(min(3, len(X_test))):
            explanation = lime_explainer.explain_instance(
                X_test[i], model.predict_proba, num_features=10
            )
            
            print(f"\nLIME Explanation for Instance {i+1}:")
            for feature, weight in explanation.as_list():
                print(f"  {feature}: {weight:.4f}")
                
    except Exception as e:
        print(f"LIME analysis failed: {e}")

# Generate feature names
def generate_feature_names(motifs):
    """Generate feature names for ML model."""
    feature_names = []
    
    for motif in motifs:
        feature_names.extend([f"{motif}_count", f"{motif}_frequency"])
    
    feature_names.extend([
        "sequence_length", "gc_content", 
        "A_frequency", "T_frequency", "G_frequency", "C_frequency"
    ])
    
    return feature_names

# Apply explainability to APP results
if 'app_ml_results' in globals() and 'app_best_motifs' in globals():
    app_feature_names = generate_feature_names(app_best_motifs)
    
    for model_name, model_data in app_ml_results.items():
        explain_predictions(
            model_data['model'], app_X_test, 
            app_feature_names, f"APP {model_name}"
        )
else:
    print('APP ML results not available. Please run the ML validation cell first.')

# Process PSEN1 Gene
print('\n' + '=' * 80)
print('PROCESSING PSEN1 GENE')
print('=' * 80)

# Get PSEN1 sequences
psen1_healthy = datasets['PSEN1']['healthy']['sequences']
psen1_unhealthy = datasets['PSEN1']['unhealthy']['sequences']

print(f'PSEN1 Healthy sequences: {len(psen1_healthy)}')
print(f'PSEN1 Unhealthy sequences: {len(psen1_unhealthy)}')

# Check if sequences exist
if not psen1_healthy or not psen1_unhealthy:
    print('ERROR: PSEN1 sequences not found. Please check data loading.')
else:
    # Run motif discovery for different motif lengths
    psen1_results = {}
    
    for motif_length in motif_lengths:
        print(f'\n--- Processing motif length {motif_length} ---')
        
        try:
            # Initialize hybrid finder
            hybrid_finder = HybridGAPSOMotifFinder(
                target_sequences=psen1_unhealthy,
                background_sequences=psen1_healthy,
                motif_length=motif_length
            )
            
            # Run optimization
            results = hybrid_finder.run_hybrid_optimization()
            psen1_results[motif_length] = results
            
            print(f'Best motif for length {motif_length}: {results["hybrid_best"]} (Score: {results["hybrid_score"]:.4f})')
            
        except Exception as e:
            print(f'Error processing motif length {motif_length}: {str(e)}')
            continue
    
    print('\nPSEN1 gene processing completed!')
    
    # Apply ML validation to PSEN1 results
    if psen1_results:
        psen1_best_motifs = [psen1_results[length]['hybrid_best'] for length in motif_lengths if length in psen1_results]
        if psen1_best_motifs:
            psen1_ml_results, psen1_X_test, psen1_y_test, psen1_scaler = validate_motifs_ml(
                psen1_healthy, psen1_unhealthy, psen1_best_motifs
            )
        else:
            print('No PSEN1 motifs found for ML validation.')
    else:
        print('No PSEN1 results available for ML validation.')

# Process PSEN2 Gene
print('\n' + '=' * 80)
print('PROCESSING PSEN2 GENE')
print('=' * 80)

# Get PSEN2 sequences
psen2_healthy = datasets['PSEN2']['healthy']['sequences']
psen2_unhealthy = datasets['PSEN2']['unhealthy']['sequences']

print(f'PSEN2 Healthy sequences: {len(psen2_healthy)}')
print(f'PSEN2 Unhealthy sequences: {len(psen2_unhealthy)}')

# Check if sequences exist
if not psen2_healthy or not psen2_unhealthy:
    print('ERROR: PSEN2 sequences not found. Please check data loading.')
else:
    # Run motif discovery for different motif lengths
    psen2_results = {}
    
    for motif_length in motif_lengths:
        print(f'\n--- Processing motif length {motif_length} ---')
        
        try:
            # Initialize hybrid finder
            hybrid_finder = HybridGAPSOMotifFinder(
                target_sequences=psen2_unhealthy,
                background_sequences=psen2_healthy,
                motif_length=motif_length
            )
            
            # Run optimization
            results = hybrid_finder.run_hybrid_optimization()
            psen2_results[motif_length] = results
            
            print(f'Best motif for length {motif_length}: {results["hybrid_best"]} (Score: {results["hybrid_score"]:.4f})')
            
        except Exception as e:
            print(f'Error processing motif length {motif_length}: {str(e)}')
            continue
    
    print('\nPSEN2 gene processing completed!')
    
    # Apply ML validation to PSEN2 results
    if psen2_results:
        psen2_best_motifs = [psen2_results[length]['hybrid_best'] for length in motif_lengths if length in psen2_results]
        if psen2_best_motifs:
            psen2_ml_results, psen2_X_test, psen2_y_test, psen2_scaler = validate_motifs_ml(
                psen2_healthy, psen2_unhealthy, psen2_best_motifs
            )
        else:
            print('No PSEN2 motifs found for ML validation.')
    else:
        print('No PSEN2 results available for ML validation.')

# Comprehensive Results Summary
def create_results_summary(all_results):
    """Create comprehensive summary of all results."""
    summary_data = []
    \n
    for gene, gene_results in all_results.items():\n
        for motif_length, results in gene_results.items():\n
            summary_data.append({\n
                'Gene': gene,\n
                'Motif_Length': motif_length,\n
                'GA_Motif': results['ga']['motif'],\n
                'GA_Score': results['ga']['score'],\n
                'PSO_Motif': results['pso']['motif'],\n
                'PSO_Score': results['pso']['score'],\n
                'Hybrid_Motif': results['hybrid_best'],\n
                'Hybrid_Score': results['hybrid_score']\n
            })\n
    \n
    return pd.DataFrame(summary_data)\n
\n
# Create final summary\n
all_results = {\n
    'APP': app_results,\n
    'PSEN1': psen1_results,\n
    'PSEN2': psen2_results\n
}\n
\n
final_summary = create_results_summary(all_results)\n
print(\"\\n\" + \"=\" * 80)\n
print(\"FINAL RESULTS SUMMARY\")\n
print(\"=\" * 80)\n
print(final_summary)\n
\n
# Save results\n
final_summary.to_csv('alzheimer_motif_discovery_results.csv', index=False)\n
print(\"\\nResults saved to 'alzheimer_motif_discovery_results.csv'\")

# Plot convergence curves for all genes\n
def plot_convergence_comparison():\n
    \"\"\"Plot convergence curves for all algorithms and genes.\"\"\"\n
    fig, axes = plt.subplots(3, 2, figsize=(15, 12))\n
    \n
    genes = ['APP', 'PSEN1', 'PSEN2']\n
    results_dict = {'APP': app_results, 'PSEN1': psen1_results, 'PSEN2': psen2_results}\n
    \n
    for i, gene in enumerate(genes):\n
        # Plot GA convergence\n
        for motif_length in [6, 8, 10]:\n
            ga_convergence = results_dict[gene][motif_length]['ga']['convergence']\n
            axes[i, 0].plot(ga_convergence, label=f'Length {motif_length}')\n
        \n
        axes[i, 0].set_title(f'{gene} - GA Convergence')\n
        axes[i, 0].set_xlabel('Generation')\n
        axes[i, 0].set_ylabel('Best Score')\n
        axes[i, 0].legend()\n
        axes[i, 0].grid(True)\n
        \n
        # Plot PSO convergence\n
        for motif_length in [6, 8, 10]:\n
            pso_convergence = results_dict[gene][motif_length]['pso']['convergence']\n
            axes[i, 1].plot(pso_convergence, label=f'Length {motif_length}')\n
        \n
        axes[i, 1].set_title(f'{gene} - PSO Convergence')\n
        axes[i, 1].set_xlabel('Iteration')\n
        axes[i, 1].set_ylabel('Best Score')\n
        axes[i, 1].legend()\n
        axes[i, 1].grid(True)\n
    \n
    plt.tight_layout()\n
    plt.show()\n
\n
plot_convergence_comparison()