# PSEN2 Diagnostic Analysis - Investigate Zero Score Issue

def diagnose_psen2_data(psen2_healthy, psen2_unhealthy):
    """
    Comprehensive diagnostic analysis of PSEN2 data to identify why motif scores are zero.
    """
    print("=" * 60)
    print("PSEN2 DIAGNOSTIC ANALYSIS")
    print("=" * 60)
    
    # 1. Basic Data Statistics
    print("\n1. BASIC DATA STATISTICS:")
    print(f"Healthy sequences: {len(psen2_healthy)}")
    print(f"Unhealthy sequences: {len(psen2_unhealthy)}")
    
    if psen2_healthy:
        healthy_lengths = [len(seq) for seq in psen2_healthy]
        print(f"Healthy sequence lengths: min={min(healthy_lengths)}, max={max(healthy_lengths)}, avg={sum(healthy_lengths)/len(healthy_lengths):.1f}")
    
    if psen2_unhealthy:
        unhealthy_lengths = [len(seq) for seq in psen2_unhealthy]
        print(f"Unhealthy sequence lengths: min={min(unhealthy_lengths)}, max={max(unhealthy_lengths)}, avg={sum(unhealthy_lengths)/len(unhealthy_lengths):.1f}")
    
    # 2. Nucleotide Composition Analysis
    print("\n2. NUCLEOTIDE COMPOSITION:")
    
    def analyze_composition(sequences, label):
        if not sequences:
            print(f"{label}: No sequences")
            return
            
        all_nucleotides = ''.join(sequences)
        total = len(all_nucleotides)
        
        if total == 0:
            print(f"{label}: Empty sequences")
            return
            
        composition = {
            'A': all_nucleotides.count('A') / total * 100,
            'T': all_nucleotides.count('T') / total * 100,
            'G': all_nucleotides.count('G') / total * 100,
            'C': all_nucleotides.count('C') / total * 100,
            'N': all_nucleotides.count('N') / total * 100,
            'Other': (total - all_nucleotides.count('A') - all_nucleotides.count('T') - 
                     all_nucleotides.count('G') - all_nucleotides.count('C') - 
                     all_nucleotides.count('N')) / total * 100
        }
        
        print(f"{label}:")
        for nt, pct in composition.items():
            if pct > 0:
                print(f"  {nt}: {pct:.2f}%")
    
    analyze_composition(psen2_healthy, "Healthy")
    analyze_composition(psen2_unhealthy, "Unhealthy")
    
    # 3. Test Motif Scoring with Known Motifs
    print("\n3. MOTIF SCORING TEST:")
    
    # Test with simple motifs
    test_motifs = ['AAAA', 'TTTT', 'GGGG', 'CCCC', 'ATCG', 'CGAT']
    
    # Import the MotifDiscovery class (assuming it's available)
    try:
        motif_discovery = MotifDiscovery(psen2_healthy)
        
        print("Testing motif scores:")
        for motif in test_motifs:
            score = motif_discovery.calculate_motif_score(motif, psen2_healthy, psen2_unhealthy)
            print(f"  {motif}: {score:.4f}")
            
    except Exception as e:
        print(f"Error testing motif scoring: {e}")
    
    # 4. Sequence Content Analysis
    print("\n4. SEQUENCE CONTENT ANALYSIS:")
    
    def check_sequence_quality(sequences, label):
        if not sequences:
            print(f"{label}: No sequences to analyze")
            return
            
        print(f"{label}:")
        
        # Check for empty sequences
        empty_count = sum(1 for seq in sequences if not seq or len(seq) == 0)
        print(f"  Empty sequences: {empty_count}")
        
        # Check for very short sequences
        short_count = sum(1 for seq in sequences if len(seq) < 50)
        print(f"  Very short sequences (<50bp): {short_count}")
        
        # Check for sequences with high N content
        high_n_count = sum(1 for seq in sequences if seq.count('N') / len(seq) > 0.1 if seq)
        print(f"  High N content (>10%): {high_n_count}")
        
        # Sample first few sequences
        print(f"  Sample sequences (first 100bp):")
        for i, seq in enumerate(sequences[:3]):
            if seq:
                print(f"    {i+1}: {seq[:100]}...")
            else:
                print(f"    {i+1}: EMPTY")
    
    check_sequence_quality(psen2_healthy, "Healthy")
    check_sequence_quality(psen2_unhealthy, "Unhealthy")
    
    # 5. Motif Occurrence Analysis
    print("\n5. MOTIF OCCURRENCE ANALYSIS:")
    
    def count_motif_occurrences(motif, sequences):
        """Count occurrences of motif in sequences."""
        if not sequences or not motif:
            return 0
            
        total_count = 0
        for seq in sequences:
            if seq and len(seq) >= len(motif):
                # Case-insensitive search
                seq_upper = seq.upper()
                motif_upper = motif.upper()
                
                # Count overlapping occurrences
                for i in range(len(seq_upper) - len(motif_upper) + 1):
                    if seq_upper[i:i+len(motif_upper)] == motif_upper:
                        total_count += 1
        
        return total_count
    
    # Test with discovered PSEN2 motifs
    psen2_motifs = ['TAGGCG', 'CGCGGGA', 'TTCGAGCG', 'ACCGTACTG', 'TCTTCCGTGT']
    
    print("Occurrence counts for discovered PSEN2 motifs:")
    for motif in psen2_motifs:
        healthy_count = count_motif_occurrences(motif, psen2_healthy)
        unhealthy_count = count_motif_occurrences(motif, psen2_unhealthy)
        
        print(f"  {motif}:")
        print(f"    Healthy: {healthy_count}")
        print(f"    Unhealthy: {unhealthy_count}")
        print(f"    Ratio: {unhealthy_count/max(healthy_count, 1):.2f}")
    
    # 6. Recommendations
    print("\n6. RECOMMENDATIONS:")
    
    # Check if sequences are too short
    if psen2_healthy and psen2_unhealthy:
        avg_healthy_len = sum(len(seq) for seq in psen2_healthy) / len(psen2_healthy)
        avg_unhealthy_len = sum(len(seq) for seq in psen2_unhealthy) / len(psen2_unhealthy)
        
        if avg_healthy_len < 1000 or avg_unhealthy_len < 1000:
            print("  ⚠️  Sequences may be too short for effective motif discovery")
            print("     Consider using shorter motif lengths (4-6 bp)")
        
        if len(psen2_healthy) < 5 or len(psen2_unhealthy) < 5:
            print("  ⚠️  Too few sequences for reliable statistical analysis")
            print("     Consider combining with other datasets or using different approach")
        
        # Check nucleotide diversity
        all_psen2 = ''.join(psen2_healthy + psen2_unhealthy)
        if all_psen2:
            unique_chars = set(all_psen2.upper())
            if len(unique_chars) < 4:
                print("  ⚠️  Low nucleotide diversity detected")
                print(f"     Only found: {sorted(unique_chars)}")
    
    print("\n" + "=" * 60)
    print("DIAGNOSTIC ANALYSIS COMPLETE")
    print("=" * 60)

# Enhanced Motif Discovery for PSEN2
def enhanced_psen2_motif_discovery(psen2_healthy, psen2_unhealthy):
    """
    Enhanced motif discovery specifically tuned for PSEN2 data characteristics.
    """
    print("\n" + "=" * 60)
    print("ENHANCED PSEN2 MOTIF DISCOVERY")
    print("=" * 60)
    
    # Use shorter motif lengths for PSEN2
    motif_lengths = [4, 5, 6]  # Shorter motifs for potentially shorter sequences
    
    # Adjusted parameters for PSEN2
    class EnhancedMotifDiscovery:
        def __init__(self, target_sequences):
            self.target_sequences = target_sequences
            
        def calculate_enhanced_score(self, motif, target_seqs, background_seqs):
            """Enhanced scoring function for PSEN2."""
            if not motif or not target_seqs or not background_seqs:
                return 0.0
            
            # Count occurrences
            target_count = 0
            background_count = 0
            
            # Count in target sequences
            for seq in target_seqs:
                if seq and len(seq) >= len(motif):
                    seq_upper = seq.upper()
                    motif_upper = motif.upper()
                    for i in range(len(seq_upper) - len(motif_upper) + 1):
                        if seq_upper[i:i+len(motif_upper)] == motif_upper:
                            target_count += 1
            
            # Count in background sequences  
            for seq in background_seqs:
                if seq and len(seq) >= len(motif):
                    seq_upper = seq.upper()
                    motif_upper = motif.upper()
                    for i in range(len(seq_upper) - len(motif_upper) + 1):
                        if seq_upper[i:i+len(motif_upper)] == motif_upper:
                            background_count += 1
            
            # Calculate total possible positions
            target_positions = sum(max(0, len(seq) - len(motif) + 1) for seq in target_seqs if seq)
            background_positions = sum(max(0, len(seq) - len(motif) + 1) for seq in background_seqs if seq)
            
            if target_positions == 0 or background_positions == 0:
                return 0.0
            
            # Calculate frequencies
            target_freq = target_count / target_positions
            background_freq = background_count / background_positions
            
            # Enhanced scoring: log-odds ratio with pseudocounts
            pseudocount = 1e-6
            score = np.log2((target_freq + pseudocount) / (background_freq + pseudocount))
            
            # Multiply by occurrence count for significance
            final_score = score * (target_count + background_count)
            
            return max(0, final_score)  # Return non-negative scores
    
    enhanced_discovery = EnhancedMotifDiscovery(psen2_healthy)
    
    print("Testing enhanced motif discovery on PSEN2:")
    
    # Test with various motif patterns
    test_patterns = [
        'AAAA', 'TTTT', 'GGGG', 'CCCC',  # Homopolymers
        'ATAT', 'CGCG', 'TATA', 'GCGC',  # Dinucleotide repeats
        'ATCG', 'CGAT', 'TACG', 'GCAT',  # Mixed patterns
    ]
    
    best_motifs = []
    
    for motif in test_patterns:
        score = enhanced_discovery.calculate_enhanced_score(motif, psen2_unhealthy, psen2_healthy)
        best_motifs.append((motif, score))
        print(f"  {motif}: {score:.4f}")
    
    # Sort by score
    best_motifs.sort(key=lambda x: x[1], reverse=True)
    
    print(f"\nTop 3 motifs for PSEN2:")
    for i, (motif, score) in enumerate(best_motifs[:3]):
        print(f"  {i+1}. {motif}: {score:.4f}")
    
    return best_motifs

print("PSEN2 diagnostic tools ready!")
print("Usage:")
print("1. diagnose_psen2_data(psen2_healthy, psen2_unhealthy)")
print("2. enhanced_psen2_motif_discovery(psen2_healthy, psen2_unhealthy)")
