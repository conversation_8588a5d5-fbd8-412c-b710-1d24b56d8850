{"cells": [{"cell_type": "markdown", "metadata": {"id": "title"}, "source": ["# Hybrid Metaheuristic Motif Discovery for Alzheimer's Disease-Associated Genes\n", "\n", "This notebook implements a hybrid GA-PSO approach for discovering motifs in Alzheimer's disease-associated genes (APP, PSEN1, PSEN2).\n", "\n", "## Workflow:\n", "1. **Data Loading**: Load and preprocess FASTA files\n", "2. **Algorithm Implementation**: GA, PSO, and Hybrid GA-PSO\n", "3. **Motif Discovery**: Process each gene with different motif lengths\n", "4. **ML Validation**: Machine learning validation of discovered motifs\n", "5. **Explainability**: SHAP and LIME analysis\n", "6. **Results**: Comprehensive analysis and visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_packages"}, "outputs": [], "source": ["# Install required packages\n", "!pip install biopython scikit-learn pandas numpy mat<PERSON><PERSON><PERSON>b seaborn\n", "!pip install deap pyswarm logomaker\n", "!pip install shap lime xgboost\n", "!pip install plotly kaleido\n", "\n", "# Alternative installation if above fails\n", "# !pip install biopython==1.79\n", "# !pip install deap==1.3.3\n", "# !conda install -c conda-forge logomaker -y"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "imports_cell"}, "outputs": [], "source": ["# Core libraries\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import random\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Bioinformatics\n", "from Bio import SeqIO\n", "from Bio.Seq import Seq\n", "try:\n", "    from Bio.SeqUtils import GC\n", "except ImportError:\n", "    # Alternative GC content calculation if import fails\n", "    def GC(seq):\n", "        if not seq:\n", "            return 0\n", "        gc_count = seq.count('G') + seq.count('C')\n", "        return (gc_count / len(seq)) * 100\n", "\n", "# Metaheuristics\n", "from deap import base, creator, tools, algorithms\n", "import pyswarm\n", "\n", "# Machine Learning\n", "from sklearn.model_selection import train_test_split, cross_val_score\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.metrics import classification_report, roc_auc_score, confusion_matrix\n", "import xgboost as xgb\n", "\n", "# Explainability\n", "import shap\n", "from lime.lime_tabular import LimeTabularExplainer\n", "\n", "# Visualization\n", "try:\n", "    import logomaker\n", "except ImportError:\n", "    print('logomaker not available, will use heatmaps for PWM visualization')\n", "    logomaker = None\n", "\n", "print('All libraries imported successfully!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "upload_data"}, "outputs": [], "source": ["# Upload FASTA files\n", "from google.colab import files\n", "\n", "print('Please upload your 6 FASTA files:')\n", "print('- APP_healthy_combined.fasta')\n", "print('- app_unhealthy_combined.fasta')\n", "print('- psen1_healthy_combined.fasta')\n", "print('- psen1_unhealthy_combined.fasta')\n", "print('- psen2_healthy_combined.fasta')\n", "print('- psen2_unhealthy_combined.fasta')\n", "\n", "uploaded = files.upload()\n", "\n", "print(f'Uploaded {len(uploaded)} files:')\n", "for filename in uploaded.keys():\n", "    print(f'  - {filename}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_data"}, "outputs": [], "source": ["# Load and parse FASTA files\n", "def load_fasta_sequences(filename):\n", "    \"\"\"Load sequences from FASTA file.\"\"\"\n", "    sequences = []\n", "    headers = []\n", "    \n", "    try:\n", "        for record in SeqIO.parse(filename, 'fasta'):\n", "            sequences.append(str(record.seq).upper())\n", "            headers.append(record.description)\n", "        \n", "        return {\n", "            'sequences': sequences,\n", "            'headers': headers,\n", "            'count': len(sequences)\n", "        }\n", "    except Exception as e:\n", "        print(f'Error loading {filename}: {e}')\n", "        return {'sequences': [], 'headers': [], 'count': 0}\n", "\n", "# Load all datasets\n", "datasets = {\n", "    'APP': {\n", "        'healthy': load_fasta_sequences('APP_healthy_combined.fasta'),\n", "        'unhealthy': load_fasta_sequences('app_unhealthy_combined.fasta')\n", "    },\n", "    'PSEN1': {\n", "        'healthy': load_fasta_sequences('psen1_healthy_combined.fasta'),\n", "        'unhealthy': load_fasta_sequences('psen1_unhealthy_combined.fasta')\n", "    },\n", "    'PSEN2': {\n", "        'healthy': load_fasta_sequences('psen2_healthy_combined.fasta'),\n", "        'unhealthy': load_fasta_sequences('psen2_unhealthy_combined.fasta')\n", "    }\n", "}\n", "\n", "# Display loading summary\n", "print('Dataset Loading Summary:')\n", "print('=' * 50)\n", "for gene, conditions in datasets.items():\n", "    healthy_count = conditions['healthy']['count']\n", "    unhealthy_count = conditions['unhealthy']['count']\n", "    print(f'{gene}: {healthy_count} healthy, {unhealthy_count} unhealthy sequences')\n", "\n", "print('\\nData loading completed!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "motif_discovery_class"}, "outputs": [], "source": ["# Motif Discovery Utility Class\n", "class MotifDiscovery:\n", "    def __init__(self, sequences, motif_length_range=(6, 10)):\n", "        self.sequences = sequences\n", "        self.motif_length_range = motif_length_range\n", "        self.nucleotides = ['A', 'T', 'G', 'C']\n", "        \n", "    def generate_random_motif(self, length):\n", "        \"\"\"Generate a random motif of given length.\"\"\"\n", "        if length <= 0:\n", "            return ''\n", "        return ''.join(random.choices(self.nucleotides, k=length))\n", "    \n", "    def count_motif_occurrences(self, motif, sequences):\n", "        \"\"\"Count occurrences of motif in sequences.\"\"\"\n", "        if not motif or not sequences:\n", "            return 0\n", "        \n", "        count = 0\n", "        for seq in sequences:\n", "            if seq and len(seq) >= len(motif):\n", "                count += seq.upper().count(motif.upper())\n", "        return count\n", "    \n", "    def calculate_motif_score(self, motif, target_sequences, background_sequences):\n", "        \"\"\"Calculate motif score based on enrichment in target vs background.\"\"\"\n", "        # Input validation\n", "        if not motif or not target_sequences or not background_sequences:\n", "            return 0.0\n", "        \n", "        # Filter out empty sequences\n", "        target_seqs = [seq for seq in target_sequences if seq and len(seq) > 0]\n", "        background_seqs = [seq for seq in background_sequences if seq and len(seq) > 0]\n", "        \n", "        if not target_seqs or not background_seqs:\n", "            return 0.0\n", "        \n", "        target_count = self.count_motif_occurrences(motif, target_seqs)\n", "        background_count = self.count_motif_occurrences(motif, background_seqs)\n", "        \n", "        # Calculate total sequence lengths\n", "        target_total_length = sum(len(seq) for seq in target_seqs)\n", "        background_total_length = sum(len(seq) for seq in background_seqs)\n", "        \n", "        # Avoid division by zero\n", "        if target_total_length == 0 or background_total_length == 0:\n", "            return 0.0\n", "        \n", "        # Calculate frequencies\n", "        target_freq = target_count / target_total_length\n", "        background_freq = background_count / background_total_length\n", "        \n", "        # Calculate enrichment score\n", "        if background_freq == 0:\n", "            if target_freq > 0:\n", "                enrichment = target_freq * 1000  # High score for unique motifs\n", "            else:\n", "                enrichment = 0.0\n", "        else:\n", "            enrichment = target_freq / background_freq\n", "        \n", "        # Final score considers both enrichment and absolute frequency\n", "        score = enrichment * target_freq * 1000  # Scale for better numeric range\n", "        return max(0.0, score)  # Ensure non-negative score\n", "    \n", "    def mutate_motif(self, motif, mutation_rate=0.1):\n", "        \"\"\"Mutate a motif by changing some nucleotides.\"\"\"\n", "        if not motif or mutation_rate <= 0:\n", "            return motif\n", "        \n", "        motif_list = list(motif.upper())\n", "        for i in range(len(motif_list)):\n", "            if random.random() < mutation_rate:\n", "                # Choose a different nucleotide\n", "                available_nucleotides = [n for n in self.nucleotides if n != motif_list[i]]\n", "                if available_nucleotides:\n", "                    motif_list[i] = random.choice(available_nucleotides)\n", "        return ''.join(motif_list)\n", "    \n", "    def crossover_motifs(self, motif1, motif2):\n", "        \"\"\"Perform crossover between two motifs.\"\"\"\n", "        if not motif1 or not motif2 or len(motif1) != len(motif2) or len(motif1) < 2:\n", "            return motif1, motif2\n", "        \n", "        motif1 = motif1.upper()\n", "        motif2 = motif2.upper()\n", "        \n", "        crossover_point = random.randint(1, len(motif1) - 1)\n", "        child1 = motif1[:crossover_point] + motif2[crossover_point:]\n", "        child2 = motif2[:crossover_point] + motif1[crossover_point:]\n", "        return child1, child2\n", "\n", "print('MotifDiscovery class implemented successfully!')"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}