{"cells": [{"cell_type": "markdown", "metadata": {"id": "title"}, "source": ["# Hybrid Metaheuristic Motif Discovery for Alzheimer's Disease-Associated Genes\n", "\n", "This notebook implements a comprehensive hybrid metaheuristic approach for discovering motifs in Alzheimer's disease-associated genes (APP, PSEN1, PSEN2) using Genetic Algorithm (GA), Particle Swarm Optimization (PSO), and their hybrid combination.\n", "\n", "## Project Overview\n", "- **Objective**: Discover motifs (length 6-10) associated with Alzheimer's disease\n", "- **Genes**: APP, PSEN1, PSEN2 (healthy vs unhealthy sequences)\n", "- **Methods**: GA → PSO → Hybrid GA-PSO → PWM → ML Validation → Explainability\n", "- **Dataset**: 6 FASTA files with genomic sequences"]}, {"cell_type": "markdown", "metadata": {"id": "imports"}, "source": ["## 1. Imports and Dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_packages", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "c573df16-c14f-4e43-f7d6-832d40e0536c"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting biopython\n", "  Downloading biopython-1.85-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (13 kB)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.12/dist-packages (1.6.1)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.12/dist-packages (2.2.2)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.12/dist-packages (2.0.2)\n", "Requirement already satisfied: matplotlib in /usr/local/lib/python3.12/dist-packages (3.10.0)\n", "Requirement already satisfied: seaborn in /usr/local/lib/python3.12/dist-packages (0.13.2)\n", "Requirement already satisfied: scipy>=1.6.0 in /usr/local/lib/python3.12/dist-packages (from scikit-learn) (1.16.2)\n", "Requirement already satisfied: joblib>=1.2.0 in /usr/local/lib/python3.12/dist-packages (from scikit-learn) (1.5.2)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /usr/local/lib/python3.12/dist-packages (from scikit-learn) (3.6.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.12/dist-packages (from pandas) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.12/dist-packages (from pandas) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.12/dist-packages (from pandas) (2025.2)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.12/dist-packages (from matplotlib) (1.3.3)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.12/dist-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.12/dist-packages (from matplotlib) (4.60.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.12/dist-packages (from matplotlib) (1.4.9)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.12/dist-packages (from matplotlib) (25.0)\n", "Requirement already satisfied: pillow>=8 in /usr/local/lib/python3.12/dist-packages (from matplotlib) (11.3.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.12/dist-packages (from matplotlib) (3.2.4)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.12/dist-packages (from python-dateutil>=2.8.2->pandas) (1.17.0)\n", "Downloading biopython-1.85-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.3/3.3 MB\u001b[0m \u001b[31m58.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: biopython\n", "Successfully installed biopython-1.85\n", "Collecting deap\n", "  Downloading deap-1.4.3-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (13 kB)\n", "Collecting pyswarm\n", "  Downloading pyswarm-0.6.tar.gz (4.3 kB)\n", "  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Collecting logomaker\n", "  Downloading logomaker-0.8.7-py3-none-any.whl.metadata (2.6 kB)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.12/dist-packages (from deap) (2.0.2)\n", "Requirement already satisfied: matplotlib in /usr/local/lib/python3.12/dist-packages (from logomaker) (3.10.0)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.12/dist-packages (from logomaker) (2.2.2)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.12/dist-packages (from matplotlib->logomaker) (1.3.3)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.12/dist-packages (from matplotlib->logomaker) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.12/dist-packages (from matplotlib->logomaker) (4.60.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.12/dist-packages (from matplotlib->logomaker) (1.4.9)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.12/dist-packages (from matplotlib->logomaker) (25.0)\n", "Requirement already satisfied: pillow>=8 in /usr/local/lib/python3.12/dist-packages (from matplotlib->logomaker) (11.3.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.12/dist-packages (from matplotlib->logomaker) (3.2.4)\n", "Requirement already satisfied: python-dateutil>=2.7 in /usr/local/lib/python3.12/dist-packages (from matplotlib->logomaker) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.12/dist-packages (from pandas->logomaker) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.12/dist-packages (from pandas->logomaker) (2025.2)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.12/dist-packages (from python-dateutil>=2.7->matplotlib->logomaker) (1.17.0)\n", "Downloading deap-1.4.3-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (135 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m136.0/136.0 kB\u001b[0m \u001b[31m8.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading logomaker-0.8.7-py3-none-any.whl (13.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.2/13.2 MB\u001b[0m \u001b[31m54.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hBuilding wheels for collected packages: pyswarm\n", "  Building wheel for pyswarm (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for pyswarm: filename=pyswarm-0.6-py3-none-any.whl size=4463 sha256=c89011dad784600ab9a047fead93e6aaca40cc941edbca3b4a3ca653f4e8cfaf\n", "  Stored in directory: /root/.cache/pip/wheels/93/15/89/3970ef96abd6123028010a90f007c4e6a2bed700db0aa2d36a\n", "Successfully built pyswarm\n", "Installing collected packages: pyswarm, deap, logomaker\n", "Successfully installed deap-1.4.3 logomaker-0.8.7 pyswarm-0.6\n", "Requirement already satisfied: shap in /usr/local/lib/python3.12/dist-packages (0.48.0)\n", "Collecting lime\n", "  Downloading lime-*******.tar.gz (275 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m275.7/275.7 kB\u001b[0m \u001b[31m14.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: xgboost in /usr/local/lib/python3.12/dist-packages (3.0.5)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.12/dist-packages (from shap) (2.0.2)\n", "Requirement already satisfied: scipy in /usr/local/lib/python3.12/dist-packages (from shap) (1.16.2)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.12/dist-packages (from shap) (1.6.1)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.12/dist-packages (from shap) (2.2.2)\n", "Requirement already satisfied: tqdm>=4.27.0 in /usr/local/lib/python3.12/dist-packages (from shap) (4.67.1)\n", "Requirement already satisfied: packaging>20.9 in /usr/local/lib/python3.12/dist-packages (from shap) (25.0)\n", "Requirement already satisfied: slicer==0.0.8 in /usr/local/lib/python3.12/dist-packages (from shap) (0.0.8)\n", "Requirement already satisfied: numba>=0.54 in /usr/local/lib/python3.12/dist-packages (from shap) (0.60.0)\n", "Requirement already satisfied: cloudpickle in /usr/local/lib/python3.12/dist-packages (from shap) (3.1.1)\n", "Requirement already satisfied: typing-extensions in /usr/local/lib/python3.12/dist-packages (from shap) (4.15.0)\n", "Requirement already satisfied: matplotlib in /usr/local/lib/python3.12/dist-packages (from lime) (3.10.0)\n", "Requirement already satisfied: scikit-image>=0.12 in /usr/local/lib/python3.12/dist-packages (from lime) (0.25.2)\n", "Requirement already satisfied: nvidia-nccl-cu12 in /usr/local/lib/python3.12/dist-packages (from xgboost) (2.27.3)\n", "Requirement already satisfied: llvmlite<0.44,>=0.43.0dev0 in /usr/local/lib/python3.12/dist-packages (from numba>=0.54->shap) (0.43.0)\n", "Requirement already satisfied: networkx>=3.0 in /usr/local/lib/python3.12/dist-packages (from scikit-image>=0.12->lime) (3.5)\n", "Requirement already satisfied: pillow>=10.1 in /usr/local/lib/python3.12/dist-packages (from scikit-image>=0.12->lime) (11.3.0)\n", "Requirement already satisfied: imageio!=2.35.0,>=2.33 in /usr/local/lib/python3.12/dist-packages (from scikit-image>=0.12->lime) (2.37.0)\n", "Requirement already satisfied: tifffile>=2022.8.12 in /usr/local/lib/python3.12/dist-packages (from scikit-image>=0.12->lime) (2025.9.9)\n", "Requirement already satisfied: lazy-loader>=0.4 in /usr/local/lib/python3.12/dist-packages (from scikit-image>=0.12->lime) (0.4)\n", "Requirement already satisfied: joblib>=1.2.0 in /usr/local/lib/python3.12/dist-packages (from scikit-learn->shap) (1.5.2)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /usr/local/lib/python3.12/dist-packages (from scikit-learn->shap) (3.6.0)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.12/dist-packages (from matplotlib->lime) (1.3.3)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.12/dist-packages (from matplotlib->lime) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.12/dist-packages (from matplotlib->lime) (4.60.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.12/dist-packages (from matplotlib->lime) (1.4.9)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.12/dist-packages (from matplotlib->lime) (3.2.4)\n", "Requirement already satisfied: python-dateutil>=2.7 in /usr/local/lib/python3.12/dist-packages (from matplotlib->lime) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.12/dist-packages (from pandas->shap) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.12/dist-packages (from pandas->shap) (2025.2)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.12/dist-packages (from python-dateutil>=2.7->matplotlib->lime) (1.17.0)\n", "Building wheels for collected packages: lime\n", "  Building wheel for lime (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for lime: filename=lime-*******-py3-none-any.whl size=283834 sha256=a198d174042d52db6e26ebee2ec044897270d01ed950bfa137f03239a96e3aa0\n", "  Stored in directory: /root/.cache/pip/wheels/e7/5d/0e/4b4fff9a47468fed5633211fb3b76d1db43fe806a17fb7486a\n", "Successfully built lime\n", "Installing collected packages: lime\n", "Successfully installed lime-*******\n", "Requirement already satisfied: plotly in /usr/local/lib/python3.12/dist-packages (5.24.1)\n", "Collecting kaleido\n", "  Downloading kaleido-1.1.0-py3-none-any.whl.metadata (5.6 kB)\n", "Requirement already satisfied: tenacity>=6.2.0 in /usr/local/lib/python3.12/dist-packages (from plotly) (8.5.0)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.12/dist-packages (from plotly) (25.0)\n", "Collecting choreographer>=1.0.10 (from kaleido)\n", "  Downloading choreographer-1.1.1-py3-none-any.whl.metadata (6.8 kB)\n", "Collecting logistro>=1.0.8 (from kaleido)\n", "  Downloading logistro-1.1.0-py3-none-any.whl.metadata (2.6 kB)\n", "Requirement already satisfied: orjson>=3.10.15 in /usr/local/lib/python3.12/dist-packages (from kaleido) (3.11.3)\n", "Collecting pytest-timeout>=2.4.0 (from kaleido)\n", "  Downloading pytest_timeout-2.4.0-py3-none-any.whl.metadata (20 kB)\n", "Requirement already satisfied: simplejson>=3.19.3 in /usr/local/lib/python3.12/dist-packages (from choreographer>=1.0.10->kaleido) (3.20.1)\n", "Requirement already satisfied: pytest>=7.0.0 in /usr/local/lib/python3.12/dist-packages (from pytest-timeout>=2.4.0->kaleido) (8.4.2)\n", "Requirement already satisfied: iniconfig>=1 in /usr/local/lib/python3.12/dist-packages (from pytest>=7.0.0->pytest-timeout>=2.4.0->kaleido) (2.1.0)\n", "Requirement already satisfied: pluggy<2,>=1.5 in /usr/local/lib/python3.12/dist-packages (from pytest>=7.0.0->pytest-timeout>=2.4.0->kaleido) (1.6.0)\n", "Requirement already satisfied: pygments>=2.7.2 in /usr/local/lib/python3.12/dist-packages (from pytest>=7.0.0->pytest-timeout>=2.4.0->kaleido) (2.19.2)\n", "Downloading kaleido-1.1.0-py3-none-any.whl (66 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m66.3/66.3 kB\u001b[0m \u001b[31m4.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading choreographer-1.1.1-py3-none-any.whl (52 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m52.3/52.3 kB\u001b[0m \u001b[31m3.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading logistro-1.1.0-py3-none-any.whl (7.9 kB)\n", "Downloading pytest_timeout-2.4.0-py3-none-any.whl (14 kB)\n", "Installing collected packages: logistro, pytest-timeout, choreographer, kaleido\n", "Successfully installed choreographer-1.1.1 kaleido-1.1.0 logistro-1.1.0 pytest-timeout-2.4.0\n"]}], "source": ["# Install required packages\n", "!pip install biopython scikit-learn pandas numpy mat<PERSON><PERSON><PERSON>b seaborn\n", "!pip install deap pyswarm logomaker\n", "!pip install shap lime xgboost\n", "!pip install plotly kaleido\n", "\n", "# Alternative installation if above fails\n", "# !pip install biopython==1.79\n", "# !pip install deap==1.3.3\n", "# !conda install -c conda-forge logomaker -y"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "imports_cell", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "602230e1-91c2-4046-bfe7-f9c0c7bc4e16"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["All dependencies imported successfully!\n"]}], "source": ["# Core libraries\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from collections import Counter, defaultdict\n", "import random\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Bioinformatics\n", "from Bio import SeqIO\n", "from Bio.Seq import Seq\n", "try:\n", "    from Bio.SeqUtils import GC\n", "except ImportError:\n", "    # Alternative GC content calculation if import fails\n", "    def GC(seq):\n", "        if not seq:\n", "            return 0\n", "        gc_count = seq.count('G') + seq.count('C')\n", "        return (gc_count / len(seq)) * 100\n", "\n", "# Metaheuristics\n", "from deap import base, creator, tools, algorithms\n", "import pyswarm\n", "\n", "# Machine Learning\n", "from sklearn.model_selection import train_test_split, cross_val_score\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score\n", "from sklearn.preprocessing import StandardScaler\n", "import xgboost as xgb\n", "\n", "# Explainability\n", "import shap\n", "import lime\n", "from lime.lime_tabular import LimeTabularExplainer\n", "\n", "# Visualization\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "import logomaker\n", "\n", "# Set random seeds for reproducibility\n", "np.random.seed(42)\n", "random.seed(42)\n", "\n", "print(\"All dependencies imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_loading"}, "source": ["## 2. Data Loading and Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "upload_files", "colab": {"base_uri": "https://localhost:8080/", "height": 400}, "outputId": "e43f43d6-9aad-449b-b8fc-8bac83eacd05"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Please upload your 6 FASTA files:\n", "1. APP_healthy_combined.fasta\n", "2. app_unhealthy_combined.fasta\n", "3. psen1_healthy_combined.fasta\n", "4. psen1_unhealthy_combined.fasta\n", "5. psen2_healthy_combined.fasta\n", "6. psen2_unhealthy_combined.fasta\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "     <input type=\"file\" id=\"files-9a25b84a-e36b-4b56-ae6e-e87caf3a04de\" name=\"files[]\" multiple disabled\n", "        style=\"border:none\" />\n", "     <output id=\"result-9a25b84a-e36b-4b56-ae6e-e87caf3a04de\">\n", "      Upload widget is only available when the cell has been executed in the\n", "      current browser session. Please rerun this cell to enable.\n", "      </output>\n", "      <script>// Copyright 2017 Google LLC\n", "//\n", "// Licensed under the Apache License, Version 2.0 (the \"License\");\n", "// you may not use this file except in compliance with the License.\n", "// You may obtain a copy of the License at\n", "//\n", "//      http://www.apache.org/licenses/LICENSE-2.0\n", "//\n", "// Unless required by applicable law or agreed to in writing, software\n", "// distributed under the License is distributed on an \"AS IS\" BASIS,\n", "// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n", "// See the License for the specific language governing permissions and\n", "// limitations under the License.\n", "\n", "/**\n", " * @fileoverview Helpers for google.colab Python module.\n", " */\n", "(function(scope) {\n", "function span(text, styleAttributes = {}) {\n", "  const element = document.createElement('span');\n", "  element.textContent = text;\n", "  for (const key of Object.keys(styleAttributes)) {\n", "    element.style[key] = styleAttributes[key];\n", "  }\n", "  return element;\n", "}\n", "\n", "// Max number of bytes which will be uploaded at a time.\n", "const MAX_PAYLOAD_SIZE = 100 * 1024;\n", "\n", "function _uploadFiles(inputId, outputId) {\n", "  const steps = uploadFilesStep(inputId, outputId);\n", "  const outputElement = document.getElementById(outputId);\n", "  // Cache steps on the outputElement to make it available for the next call\n", "  // to uploadFilesContinue from Python.\n", "  outputElement.steps = steps;\n", "\n", "  return _uploadFilesContinue(outputId);\n", "}\n", "\n", "// This is roughly an async generator (not supported in the browser yet),\n", "// where there are multiple asynchronous steps and the Python side is going\n", "// to poll for completion of each step.\n", "// This uses a Promise to block the python side on completion of each step,\n", "// then passes the result of the previous step as the input to the next step.\n", "function _uploadFilesContinue(outputId) {\n", "  const outputElement = document.getElementById(outputId);\n", "  const steps = outputElement.steps;\n", "\n", "  const next = steps.next(outputElement.lastPromiseValue);\n", "  return Promise.resolve(next.value.promise).then((value) => {\n", "    // Cache the last promise value to make it available to the next\n", "    // step of the generator.\n", "    outputElement.lastPromiseValue = value;\n", "    return next.value.response;\n", "  });\n", "}\n", "\n", "/**\n", " * Generator function which is called between each async step of the upload\n", " * process.\n", " * @param {string} inputId Element ID of the input file picker element.\n", " * @param {string} outputId Element ID of the output display.\n", " * @return {!Iterable<!Object>} Iterable of next steps.\n", " */\n", "function* uploadFilesStep(inputId, outputId) {\n", "  const inputElement = document.getElementById(inputId);\n", "  inputElement.disabled = false;\n", "\n", "  const outputElement = document.getElementById(outputId);\n", "  outputElement.innerHTML = '';\n", "\n", "  const pickedPromise = new Promise((resolve) => {\n", "    inputElement.addEventListener('change', (e) => {\n", "      resolve(e.target.files);\n", "    });\n", "  });\n", "\n", "  const cancel = document.createElement('button');\n", "  inputElement.parentElement.appendChild(cancel);\n", "  cancel.textContent = 'Cancel upload';\n", "  const cancelPromise = new Promise((resolve) => {\n", "    cancel.onclick = () => {\n", "      resolve(null);\n", "    };\n", "  });\n", "\n", "  // Wait for the user to pick the files.\n", "  const files = yield {\n", "    promise: Promise.race([pickedPromise, cancelPromise]),\n", "    response: {\n", "      action: 'starting',\n", "    }\n", "  };\n", "\n", "  cancel.remove();\n", "\n", "  // Disable the input element since further picks are not allowed.\n", "  inputElement.disabled = true;\n", "\n", "  if (!files) {\n", "    return {\n", "      response: {\n", "        action: 'complete',\n", "      }\n", "    };\n", "  }\n", "\n", "  for (const file of files) {\n", "    const li = document.createElement('li');\n", "    li.append(span(file.name, {fontWeight: 'bold'}));\n", "    li.append(span(\n", "        `(${file.type || 'n/a'}) - ${file.size} bytes, ` +\n", "        `last modified: ${\n", "            file.lastModifiedDate ? file.lastModifiedDate.toLocaleDateString() :\n", "                                    'n/a'} - `));\n", "    const percent = span('0% done');\n", "    li.append<PERSON><PERSON>d(percent);\n", "\n", "    outputElement.appendChild(li);\n", "\n", "    const fileDataPromise = new Promise((resolve) => {\n", "      const reader = new FileReader();\n", "      reader.onload = (e) => {\n", "        resolve(e.target.result);\n", "      };\n", "      reader.readAsArrayBuffer(file);\n", "    });\n", "    // Wait for the data to be ready.\n", "    let fileData = yield {\n", "      promise: fileDataPromise,\n", "      response: {\n", "        action: 'continue',\n", "      }\n", "    };\n", "\n", "    // Use a chunked sending to avoid message size limits. See b/62115660.\n", "    let position = 0;\n", "    do {\n", "      const length = Math.min(fileData.byteLength - position, MAX_PAYLOAD_SIZE);\n", "      const chunk = new Uint8Array(fileData, position, length);\n", "      position += length;\n", "\n", "      const base64 = btoa(String.fromCharCode.apply(null, chunk));\n", "      yield {\n", "        response: {\n", "          action: 'append',\n", "          file: file.name,\n", "          data: base64,\n", "        },\n", "      };\n", "\n", "      let percentDone = fileData.byteLength === 0 ?\n", "          100 :\n", "          Math.round((position / fileData.byteLength) * 100);\n", "      percent.textContent = `${percentDone}% done`;\n", "\n", "    } while (position < fileData.byteLength);\n", "  }\n", "\n", "  // All done.\n", "  yield {\n", "    response: {\n", "      action: 'complete',\n", "    }\n", "  };\n", "}\n", "\n", "scope.google = scope.google || {};\n", "scope.google.colab = scope.google.colab || {};\n", "scope.google.colab._files = {\n", "  _uploadFiles,\n", "  _uploadFilesContinue,\n", "};\n", "})(self);\n", "</script> "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Saving APP_healthy_combined.fasta to APP_healthy_combined.fasta\n", "Saving app_unhealthy_combined.fasta to app_unhealthy_combined.fasta\n", "Saving psen1_healthy_combined.fasta to psen1_healthy_combined.fasta\n", "Saving psen1_unhealthy_combined.fasta to psen1_unhealthy_combined.fasta\n", "Saving psen2_healthy_combined.fasta to psen2_healthy_combined.fasta\n", "Saving psen2_unhealthy_combined.fasta to psen2_unhealthy_combined.fasta\n", "\n", "Uploaded 6 files successfully!\n"]}], "source": ["# Upload FASTA files\n", "from google.colab import files\n", "\n", "print(\"Please upload your 6 FASTA files:\")\n", "print(\"1. APP_healthy_combined.fasta\")\n", "print(\"2. app_unhealthy_combined.fasta\")\n", "print(\"3. psen1_healthy_combined.fasta\")\n", "print(\"4. psen1_unhealthy_combined.fasta\")\n", "print(\"5. psen2_healthy_combined.fasta\")\n", "print(\"6. psen2_unhealthy_combined.fasta\")\n", "\n", "uploaded = files.upload()\n", "print(f\"\\nUploaded {len(uploaded)} files successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_sequences", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "96c5915c-3a43-44d6-8c9b-2fe6022a5cd8"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Loaded 7 sequences from APP_healthy_combined.fasta\n", "First sequence length: 290578\n", "First header: NC_000021.9:25880550-26171128_1 Homo sapiens chromosome 21, GRCh38.p14 Primary Assembly...\n", "Loaded 6 sequences from app_unhealthy_combined.fasta\n", "First sequence length: 290576\n", "First header: NC_000014.9:25880550-26171128_1 Homo sapiens chromosome 14, GRCh38.p14 Primary Assembly...\n", "\n", "APP gene loaded successfully!\n", "--------------------------------------------------\n", "Loaded 10 sequences from psen1_healthy_combined.fasta\n", "First sequence length: 87275\n", "First header: NC_000014.9:73136417-73223691_1 Homo sapiens chromosome 14, GRCh38.p14 Primary Assembly...\n", "Loaded 26 sequences from psen1_unhealthy_combined.fasta\n", "First sequence length: 1000\n", "First header: NM_000021.4:1-1000 Homo sapiens presenilin 1 (PSEN1), transcript variant 1, mRNA...\n", "\n", "PSEN1 gene loaded successfully!\n", "--------------------------------------------------\n", "Loaded 50 sequences from psen2_healthy_combined.fasta\n", "First sequence length: 64543\n", "First header: NG_007381.2:1-5415 Homo sapiens presenilin 2 (PSEN2), RefSeqGene (LRG_225) on chromosome 1...\n", "Loaded 6 sequences from psen2_unhealthy_combined.fasta\n", "First sequence length: 33050\n", "First header: NC_000001.11:226870616-226903668_1 Homo sapiens chromosome 1, GRCh38.p14 Primary Assembly...\n", "\n", "PSEN2 gene loaded successfully!\n", "--------------------------------------------------\n"]}], "source": ["def load_fasta_sequences(filename):\n", "    \"\"\"\n", "    Load sequences from FASTA file, preserving headers and sequences as they are.\n", "    \"\"\"\n", "    sequences = []\n", "    headers = []\n", "\n", "    try:\n", "        for record in SeqIO.parse(filename, \"fasta\"):\n", "            headers.append(record.description)\n", "            sequences.append(str(record.seq).upper())\n", "\n", "        print(f\"Loaded {len(sequences)} sequences from {filename}\")\n", "        if sequences:\n", "            print(f\"First sequence length: {len(sequences[0])}\")\n", "            print(f\"First header: {headers[0][:100]}...\")\n", "\n", "        return sequences, headers\n", "    except Exception as e:\n", "        print(f\"Error loading {filename}: {e}\")\n", "        return [], []\n", "\n", "# Load all datasets\n", "datasets = {}\n", "gene_files = {\n", "    'APP': {\n", "        'healthy': 'APP_healthy_combined.fasta',\n", "        'unhealthy': 'app_unhealthy_combined.fasta'\n", "    },\n", "    'PSEN1': {\n", "        'healthy': 'psen1_healthy_combined.fasta',\n", "        'unhealthy': 'psen1_unhealthy_combined.fasta'\n", "    },\n", "    'PSEN2': {\n", "        'healthy': 'psen2_healthy_combined.fasta',\n", "        'unhealthy': 'psen2_unhealthy_combined.fasta'\n", "    }\n", "}\n", "\n", "for gene in gene_files:\n", "    datasets[gene] = {}\n", "    for condition in ['healthy', 'unhealthy']:\n", "        filename = gene_files[gene][condition]\n", "        sequences, headers = load_fasta_sequences(filename)\n", "        datasets[gene][condition] = {\n", "            'sequences': sequences,\n", "            'headers': headers\n", "        }\n", "    print(f\"\\n{gene} gene loaded successfully!\")\n", "    print(\"-\" * 50)"]}, {"cell_type": "markdown", "metadata": {"id": "data_viz"}, "source": ["## 3. Dataset Visualization and Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "dataset_stats", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "e15fe209-336f-48bc-e89a-c3c0daaa20a6"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Dataset Summary:\n", "    gene  condition  num_sequences  total_length  avg_length  avg_gc_content\n", "0    APP    healthy              7       2034050   290578.57           39.73\n", "1    APP  unhealthy              6       1743466   290577.67           37.82\n", "2  PSEN1    healthy             10        872748    87274.80           42.73\n", "3  PSEN1  unhealthy             26        116216     4469.85           44.60\n", "4  PSEN2    healthy             50       3301949    66038.98           47.37\n", "5  PSEN2  unhealthy              6        198315    33052.50           51.04\n"]}], "source": ["def analyze_sequences(sequences, gene_name, condition):\n", "    \"\"\"\n", "    Analyze basic statistics of sequences.\n", "    \"\"\"\n", "    if not sequences:\n", "        return {}\n", "\n", "    lengths = [len(seq) for seq in sequences]\n", "    gc_contents = [GC(seq) for seq in sequences if seq]\n", "\n", "    # Nucleotide composition\n", "    all_nucleotides = ''.join(sequences)\n", "    nucleotide_counts = Counter(all_nucleotides)\n", "    total_nucleotides = sum(nucleotide_counts.values())\n", "\n", "    stats = {\n", "        'gene': gene_name,\n", "        'condition': condition,\n", "        'num_sequences': len(sequences),\n", "        'total_length': sum(lengths),\n", "        'avg_length': np.mean(lengths),\n", "        'min_length': min(lengths),\n", "        'max_length': max(lengths),\n", "        'avg_gc_content': np.mean(gc_contents) if gc_contents else 0,\n", "        'nucleotide_composition': {\n", "            nt: count/total_nucleotides*100 for nt, count in nucleotide_counts.items()\n", "        }\n", "    }\n", "\n", "    return stats\n", "\n", "# Analyze all datasets\n", "all_stats = []\n", "for gene in datasets:\n", "    for condition in ['healthy', 'unhealthy']:\n", "        sequences = datasets[gene][condition]['sequences']\n", "        stats = analyze_sequences(sequences, gene, condition)\n", "        if stats:\n", "            all_stats.append(stats)\n", "\n", "# Create summary DataFrame\n", "summary_df = pd.DataFrame(all_stats)\n", "print(\"Dataset Summary:\")\n", "print(summary_df[['gene', 'condition', 'num_sequences', 'total_length', 'avg_length', 'avg_gc_content']].round(2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "visualize_data", "colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "outputId": "cf6f9567-a421-4e57-bef7-9da177f7d6c6"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1500x1000 with 4 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["\n", "Dataset visualization completed!\n"]}], "source": ["# Visualization of dataset characteristics\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# 1. Sequence counts\n", "genes = summary_df['gene'].unique()\n", "healthy_counts = [summary_df[(summary_df['gene']==g) & (summary_df['condition']=='healthy')]['num_sequences'].iloc[0] for g in genes]\n", "unhealthy_counts = [summary_df[(summary_df['gene']==g) & (summary_df['condition']=='unhealthy')]['num_sequences'].iloc[0] for g in genes]\n", "\n", "x = np.arange(len(genes))\n", "width = 0.35\n", "\n", "axes[0,0].bar(x - width/2, healthy_counts, width, label='Healthy', color='lightblue')\n", "axes[0,0].bar(x + width/2, unhealthy_counts, width, label='Unhealthy', color='lightcoral')\n", "axes[0,0].set_xlabel('Genes')\n", "axes[0,0].set_ylabel('Number of Sequences')\n", "axes[0,0].set_title('Sequence Counts by Gene and Condition')\n", "axes[0,0].set_xticks(x)\n", "axes[0,0].set_xticklabels(genes)\n", "axes[0,0].legend()\n", "\n", "# 2. Average sequence lengths\n", "healthy_lengths = [summary_df[(summary_df['gene']==g) & (summary_df['condition']=='healthy')]['avg_length'].iloc[0] for g in genes]\n", "unhealthy_lengths = [summary_df[(summary_df['gene']==g) & (summary_df['condition']=='unhealthy')]['avg_length'].iloc[0] for g in genes]\n", "\n", "axes[0,1].bar(x - width/2, healthy_lengths, width, label='Healthy', color='lightblue')\n", "axes[0,1].bar(x + width/2, unhealthy_lengths, width, label='Unhealthy', color='lightcoral')\n", "axes[0,1].set_xlabel('Genes')\n", "axes[0,1].set_ylabel('Average Sequence Length')\n", "axes[0,1].set_title('Average Sequence Lengths')\n", "axes[0,1].set_xticks(x)\n", "axes[0,1].set_xticklabels(genes)\n", "axes[0,1].legend()\n", "\n", "# 3. GC Content\n", "healthy_gc = [summary_df[(summary_df['gene']==g) & (summary_df['condition']=='healthy')]['avg_gc_content'].iloc[0] for g in genes]\n", "unhealthy_gc = [summary_df[(summary_df['gene']==g) & (summary_df['condition']=='unhealthy')]['avg_gc_content'].iloc[0] for g in genes]\n", "\n", "axes[1,0].bar(x - width/2, healthy_gc, width, label='Healthy', color='lightblue')\n", "axes[1,0].bar(x + width/2, unhealthy_gc, width, label='Unhealthy', color='lightcoral')\n", "axes[1,0].set_xlabel('Genes')\n", "axes[1,0].set_ylabel('Average GC Content (%)')\n", "axes[1,0].set_title('GC Content Comparison')\n", "axes[1,0].set_xticks(x)\n", "axes[1,0].set_xticklabels(genes)\n", "axes[1,0].legend()\n", "\n", "# 4. Nucleotide composition for first gene (APP)\n", "app_healthy_comp = summary_df[(summary_df['gene']=='APP') & (summary_df['condition']=='healthy')]['nucleotide_composition'].iloc[0]\n", "nucleotides = list(app_healthy_comp.keys())\n", "compositions = list(app_healthy_comp.values())\n", "\n", "axes[1,1].pie(compositions, labels=nucleotides, autopct='%1.1f%%', startangle=90)\n", "axes[1,1].set_title('Nucleotide Composition (APP Healthy)')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\nDataset visualization completed!\")"]}, {"cell_type": "markdown", "metadata": {"id": "motif_discovery_loop"}, "source": ["## 4. Motif <PERSON> for Each Gene\\n\\nWe'll process each gene (APP, PSEN1, PSEN2) sequentially with:\\n1. GA implementation for exploration\\n2. PSO implementation for optimization\\n3. Hybrid GA-PSO for exploitation and convergence\\n4. PWM analysis and comparison\\n5. ML validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "motif_utils", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "69ec8996-a6a8-4d77-fdb2-3cb3c83b0442"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Motif discovery utilities defined!\n"]}], "source": ["# Motif Discovery Utility Functions - Colab Compatible\n", "\n", "import random\n", "\n", "class MotifDiscovery:\n", "    def __init__(self, sequences, motif_length_range=(6, 10)):\n", "        self.sequences = sequences\n", "        self.motif_length_range = motif_length_range\n", "        self.nucleotides = ['A', 'T', 'G', 'C']\n", "\n", "    def generate_random_motif(self, length):\n", "        \"\"\"Generate a random motif of given length.\"\"\"\n", "        if length <= 0:\n", "            return ''\n", "        return ''.join(random.choices(self.nucleotides, k=length))\n", "\n", "    def count_motif_occurrences(self, motif, sequences):\n", "        \"\"\"Count occurrences of motif in sequences.\"\"\"\n", "        if not motif or not sequences:\n", "            return 0\n", "\n", "        count = 0\n", "        for seq in sequences:\n", "            if seq and len(seq) >= len(motif):\n", "                count += seq.upper().count(motif.upper())\n", "        return count\n", "\n", "    def calculate_motif_score(self, motif, target_sequences, background_sequences):\n", "        \"\"\"Calculate motif score based on enrichment in target vs background.\"\"\"\n", "        # Input validation\n", "        if not motif or not target_sequences or not background_sequences:\n", "            return 0.0\n", "\n", "        # Filter out empty sequences\n", "        target_seqs = [seq for seq in target_sequences if seq and len(seq) > 0]\n", "        background_seqs = [seq for seq in background_sequences if seq and len(seq) > 0]\n", "\n", "        if not target_seqs or not background_seqs:\n", "            return 0.0\n", "\n", "        target_count = self.count_motif_occurrences(motif, target_seqs)\n", "        background_count = self.count_motif_occurrences(motif, background_seqs)\n", "\n", "        # Calculate total sequence lengths\n", "        target_total_length = sum(len(seq) for seq in target_seqs)\n", "        background_total_length = sum(len(seq) for seq in background_seqs)\n", "\n", "        # Avoid division by zero\n", "        if target_total_length == 0 or background_total_length == 0:\n", "            return 0.0\n", "\n", "        # Calculate frequencies\n", "        target_freq = target_count / target_total_length\n", "        background_freq = background_count / background_total_length\n", "\n", "        # Calculate enrichment score\n", "        if background_freq == 0:\n", "            if target_freq > 0:\n", "                enrichment = target_freq * 1000  # High score for unique motifs\n", "            else:\n", "                enrichment = 0.0\n", "        else:\n", "            enrichment = target_freq / background_freq\n", "\n", "        # Final score considers both enrichment and absolute frequency\n", "        score = enrichment * target_freq * 1000  # Scale for better numeric range\n", "        return max(0.0, score)  # Ensure non-negative score\n", "\n", "    def mutate_motif(self, motif, mutation_rate=0.1):\n", "        \"\"\"Mutate a motif by changing some nucleotides.\"\"\"\n", "        if not motif or mutation_rate <= 0:\n", "            return motif\n", "\n", "        motif_list = list(motif.upper())\n", "        for i in range(len(motif_list)):\n", "            if random.random() < mutation_rate:\n", "                # Choose a different nucleotide\n", "                available_nucleotides = [n for n in self.nucleotides if n != motif_list[i]]\n", "                if available_nucleotides:\n", "                    motif_list[i] = random.choice(available_nucleotides)\n", "        return ''.join(motif_list)\n", "\n", "    def crossover_motifs(self, motif1, motif2):\n", "        \"\"\"Perform crossover between two motifs.\"\"\"\n", "        if not motif1 or not motif2 or len(motif1) != len(motif2) or len(motif1) < 2:\n", "            return motif1, motif2\n", "\n", "        motif1 = motif1.upper()\n", "        motif2 = motif2.upper()\n", "\n", "        crossover_point = random.randint(1, len(motif1) - 1)\n", "        child1 = motif1[:crossover_point] + motif2[crossover_point:]\n", "        child2 = motif2[:crossover_point] + motif1[crossover_point:]\n", "        return child1, child2\n", "\n", "print(\"Motif discovery utilities defined!\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ga_implementation", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "e281efe0-4b63-4a12-e904-3a57eb7e95cc"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Genetic Algorithm implementation ready for Colab!\n"]}], "source": ["# Genetic Algorithm Motif Finder - Colab Compatible\n", "\n", "# Required imports\n", "import numpy as np\n", "import random\n", "\n", "# Placeholder MotifDiscovery class\n", "# Replace with your actual implementation\n", "class MotifDiscovery:\n", "    def __init__(self, target_sequences):\n", "        self.target_sequences = target_sequences\n", "\n", "    def generate_random_motif(self, length):\n", "        \"\"\"Generate a random motif of given length.\"\"\"\n", "        bases = ['A', 'C', 'G', 'T']\n", "        return ''.join(random.choices(bases, k=length))\n", "\n", "    def calculate_motif_score(self, motif, target_sequences, background_sequences):\n", "        \"\"\"Dummy fitness calculation: count occurrences in target minus background.\"\"\"\n", "        target_count = sum(seq.count(motif) for seq in target_sequences)\n", "        background_count = sum(seq.count(motif) for seq in background_sequences)\n", "        return target_count - background_count\n", "\n", "    def crossover_motifs(self, parent1, parent2):\n", "        \"\"\"Single-point crossover.\"\"\"\n", "        point = random.randint(1, len(parent1)-1)\n", "        child1 = parent1[:point] + parent2[point:]\n", "        child2 = parent2[:point] + parent1[point:]\n", "        return child1, child2\n", "\n", "    def mutate_motif(self, motif, mutation_rate=0.1):\n", "        \"\"\"Randomly mutate motif based on mutation_rate.\"\"\"\n", "        bases = ['A', 'C', 'G', 'T']\n", "        motif_list = list(motif)\n", "        for i in range(len(motif_list)):\n", "            if random.random() < mutation_rate:\n", "                motif_list[i] = random.choice(bases)\n", "        return ''.join(motif_list)\n", "\n", "# Genetic Algorithm Implementation\n", "class GeneticAlgorithmMotifFinder:\n", "    def __init__(self, target_sequences, background_sequences,\n", "                 population_size=100, generations=50, motif_length=8):\n", "        self.target_sequences = target_sequences\n", "        self.background_sequences = background_sequences\n", "        self.population_size = population_size\n", "        self.generations = generations\n", "        self.motif_length = motif_length\n", "        self.motif_discovery = MotifDiscovery(target_sequences)\n", "        self.best_scores = []\n", "        self.avg_scores = []\n", "\n", "    def initialize_population(self):\n", "        \"\"\"Initialize random population of motifs.\"\"\"\n", "        return [self.motif_discovery.generate_random_motif(self.motif_length)\n", "                for _ in range(self.population_size)]\n", "\n", "    def evaluate_fitness(self, motif):\n", "        \"\"\"Evaluate fitness of a motif.\"\"\"\n", "        return self.motif_discovery.calculate_motif_score(\n", "            motif, self.target_sequences, self.background_sequences)\n", "\n", "    def selection(self, population, fitness_scores, k=3):\n", "        \"\"\"Tournament selection.\"\"\"\n", "        selected = []\n", "        for _ in range(len(population)):\n", "            tournament_indices = random.sample(range(len(population)), k)\n", "            tournament_fitness = [fitness_scores[i] for i in tournament_indices]\n", "            winner_index = tournament_indices[np.argmax(tournament_fitness)]\n", "            selected.append(population[winner_index])\n", "        return selected\n", "\n", "    def evolve(self):\n", "        \"\"\"Run the genetic algorithm.\"\"\"\n", "        population = self.initialize_population()\n", "\n", "        for generation in range(self.generations):\n", "            # Evaluate fitness\n", "            fitness_scores = [self.evaluate_fitness(motif) for motif in population]\n", "\n", "            # Track statistics\n", "            self.best_scores.append(max(fitness_scores))\n", "            self.avg_scores.append(np.mean(fitness_scores))\n", "\n", "            # Selection\n", "            selected = self.selection(population, fitness_scores)\n", "\n", "            # Crossover and mutation\n", "            new_population = []\n", "            for i in range(0, len(selected), 2):\n", "                parent1 = selected[i]\n", "                parent2 = selected[i + 1] if i + 1 < len(selected) else selected[0]\n", "\n", "                # Crossover\n", "                if random.random() < 0.8:  # Crossover probability\n", "                    child1, child2 = self.motif_discovery.crossover_motifs(parent1, parent2)\n", "                else:\n", "                    child1, child2 = parent1, parent2\n", "\n", "                # Mutation\n", "                child1 = self.motif_discovery.mutate_motif(child1, 0.1)\n", "                child2 = self.motif_discovery.mutate_motif(child2, 0.1)\n", "\n", "                new_population.extend([child1, child2])\n", "\n", "            population = new_population[:self.population_size]\n", "\n", "            if generation % 10 == 0:\n", "                print(f\"Generation {generation}: Best score = {max(fitness_scores):.4f}\")\n", "\n", "        # Return best motif\n", "        final_fitness = [self.evaluate_fitness(motif) for motif in population]\n", "        best_index = np.argmax(final_fitness)\n", "        best_motif = population[best_index]\n", "        best_score = final_fitness[best_index]\n", "\n", "        return best_motif, best_score, population\n", "\n", "print(\"Genetic Algorithm implementation ready for Colab!\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "pso_implementation", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "7fc653cb-5904-4499-f799-702e93761663"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["PSO implementation ready for Colab!\n"]}], "source": ["# Particle Swarm Optimization Mo<PERSON>f <PERSON>er - Colab Compatible\n", "\n", "import numpy as np\n", "\n", "class PSOMotifFinder:\n", "    def __init__(self, target_sequences, background_sequences,\n", "                 swarm_size=50, iterations=100, motif_length=8):\n", "        self.target_sequences = target_sequences\n", "        self.background_sequences = background_sequences\n", "        self.swarm_size = swarm_size\n", "        self.iterations = iterations\n", "        self.motif_length = motif_length\n", "        self.motif_discovery = MotifDiscovery(target_sequences)\n", "        self.nucleotides = ['A', 'T', 'G', 'C']\n", "        self.best_scores = []\n", "        self.avg_scores = []\n", "\n", "    def motif_to_vector(self, motif):\n", "        \"\"\"Convert motif string to numerical vector.\"\"\"\n", "        vector = []\n", "        for nucleotide in motif:\n", "            if nucleotide == 'A':\n", "                vector.extend([1, 0, 0, 0])\n", "            elif nucleotide == 'T':\n", "                vector.extend([0, 1, 0, 0])\n", "            elif nucleotide == 'G':\n", "                vector.extend([0, 0, 1, 0])\n", "            elif nucleotide == 'C':\n", "                vector.extend([0, 0, 0, 1])\n", "        return np.array(vector)\n", "\n", "    def vector_to_motif(self, vector):\n", "        \"\"\"Convert numerical vector to motif string.\"\"\"\n", "        motif = ''\n", "        for i in range(0, len(vector), 4):\n", "            nucleotide_probs = vector[i:i+4]\n", "            # Apply softmax to get probabilities\n", "            exp_probs = np.exp(nucleotide_probs - np.max(nucleotide_probs))\n", "            probs = exp_probs / np.sum(exp_probs)\n", "            # Choose nucleotide based on highest probability\n", "            chosen_idx = np.argmax(probs)\n", "            motif += self.nucleotides[chosen_idx]\n", "        return motif\n", "\n", "    def fitness_function(self, vector):\n", "        \"\"\"Fitness function for PSO.\"\"\"\n", "        motif = self.vector_to_motif(vector)\n", "        return self.motif_discovery.calculate_motif_score(\n", "            motif, self.target_sequences, self.background_sequences)\n", "\n", "    def optimize(self, initial_motifs=None):\n", "        \"\"\"Run PSO optimization.\"\"\"\n", "        # Initialize swarm\n", "        if initial_motifs:\n", "            particles = [self.motif_to_vector(m) for m in initial_motifs[:self.swarm_size]]\n", "            while len(particles) < self.swarm_size:\n", "                random_motif = self.motif_discovery.generate_random_motif(self.motif_length)\n", "                particles.append(self.motif_to_vector(random_motif))\n", "        else:\n", "            particles = [self.motif_to_vector(\n", "                self.motif_discovery.generate_random_motif(self.motif_length))\n", "                for _ in range(self.swarm_size)]\n", "\n", "        particles = np.array(particles)\n", "        velocities = np.random.uniform(-1, 1, particles.shape)\n", "\n", "        # Initialize personal and global bests\n", "        personal_best_positions = particles.copy()\n", "        personal_best_scores = np.array([self.fitness_function(p) for p in particles])\n", "\n", "        global_best_idx = np.argmax(personal_best_scores)\n", "        global_best_position = personal_best_positions[global_best_idx].copy()\n", "        global_best_score = personal_best_scores[global_best_idx]\n", "\n", "        # PSO parameters\n", "        w = 0.7   # Inertia weight\n", "        c1 = 1.5  # Cognitive parameter\n", "        c2 = 1.5  # Social parameter\n", "\n", "        for iteration in range(self.iterations):\n", "            for i in range(self.swarm_size):\n", "                r1, r2 = np.random.random(2)\n", "                velocities[i] = (w * velocities[i] +\n", "                                 c1 * r1 * (personal_best_positions[i] - particles[i]) +\n", "                                 c2 * r2 * (global_best_position - particles[i]))\n", "                particles[i] += velocities[i]\n", "\n", "                fitness = self.fitness_function(particles[i])\n", "\n", "                if fitness > personal_best_scores[i]:\n", "                    personal_best_scores[i] = fitness\n", "                    personal_best_positions[i] = particles[i].copy()\n", "\n", "                if fitness > global_best_score:\n", "                    global_best_score = fitness\n", "                    global_best_position = particles[i].copy()\n", "\n", "            # Track statistics\n", "            current_scores = [self.fitness_function(p) for p in particles]\n", "            self.best_scores.append(max(current_scores))\n", "            self.avg_scores.append(np.mean(current_scores))\n", "\n", "            if iteration % 20 == 0:\n", "                print(f\"Iteration {iteration}: Best score = {global_best_score:.4f}\")\n", "\n", "        best_motif = self.vector_to_motif(global_best_position)\n", "        return best_motif, global_best_score, [self.vector_to_motif(p) for p in particles]\n", "\n", "print(\"PSO implementation ready for Colab!\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "hybrid_implementation", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "0a3ecff8-8f67-495f-e3d1-8a179193c0d4"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Hybrid GA-PSO implementation ready for Colab!\n"]}], "source": ["# Hybrid GA-PSO Motif Finder - Colab Compatible\n", "\n", "class HybridGAPSOMotifFinder:\n", "    def __init__(self, target_sequences, background_sequences, motif_length=8):\n", "        self.target_sequences = target_sequences\n", "        self.background_sequences = background_sequences\n", "        self.motif_length = motif_length\n", "        self.motif_discovery = MotifDiscovery(target_sequences)\n", "\n", "    def run_hybrid_optimization(self):\n", "        \"\"\"Run hybrid GA-PSO optimization.\"\"\"\n", "        print(\"\\n=== Running Hybrid GA-PSO Optimization ===\")\n", "\n", "        # Step 1: Run GA for exploration\n", "        print(\"Step 1: Running Genetic Algorithm for exploration...\")\n", "        ga = GeneticAlgorithmMotifFinder(\n", "            self.target_sequences, self.background_sequences,\n", "            population_size=100, generations=30, motif_length=self.motif_length\n", "        )\n", "        ga_best_motif, ga_best_score, ga_population = ga.evolve()\n", "        print(f\"GA Results: Best motif = {ga_best_motif}, Score = {ga_best_score:.4f}\")\n", "\n", "        # Step 2: Use GA results as initial population for PSO\n", "        print(\"\\nStep 2: Running PSO with GA results as initial population...\")\n", "        # GA population is already list of motifs; ensure string format\n", "        ga_motifs = [''.join(ind) if isinstance(ind, list) else ind for ind in ga_population]\n", "\n", "        pso = PSOMotifFinder(\n", "            self.target_sequences, self.background_sequences,\n", "            swarm_size=50, iterations=50, motif_length=self.motif_length\n", "        )\n", "        pso_best_motif, pso_best_score, pso_population = pso.optimize(ga_motifs)\n", "        print(f\"PSO Results: Best motif = {pso_best_motif}, Score = {pso_best_score:.4f}\")\n", "\n", "        # Return results from both algorithms\n", "        return {\n", "            'ga': {'motif': ga_best_motif, 'score': ga_best_score, 'convergence': ga.best_scores},\n", "            'pso': {'motif': pso_best_motif, 'score': pso_best_score, 'convergence': pso.best_scores},\n", "            'hybrid_best': pso_best_motif if pso_best_score > ga_best_score else ga_best_motif,\n", "            'hybrid_score': max(pso_best_score, ga_best_score)\n", "        }\n", "\n", "print(\"Hybrid GA-PSO implementation ready for Colab!\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "test_implementation", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "365f518b-8abe-4365-9ab5-a5f8f640d756"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Testing implementation...\n", "\n", "Datasets loaded successfully\n", "APP: 7 healthy, 6 unhealthy sequences\n", "PSEN1: 10 healthy, 26 unhealthy sequences\n", "PSEN2: 50 healthy, 6 unhealthy sequences\n", "Generated test motif: GACAGG\n", "Motif discovery classes working correctly\n", "\n", "Implementation test completed!\n"]}], "source": ["# Test the implementation with a small example\n", "print('Testing implementation...\\n')\n", "\n", "# Check if datasets are loaded\n", "if 'datasets' in globals():\n", "    print('Datasets loaded successfully')\n", "    for gene in ['APP', 'PSEN1', 'PSEN2']:\n", "        if gene in datasets:\n", "            healthy_count = len(datasets[gene]['healthy']['sequences'])\n", "            unhealthy_count = len(datasets[gene]['unhealthy']['sequences'])\n", "            print(f'{gene}: {healthy_count} healthy, {unhealthy_count} unhealthy sequences')\n", "        else:\n", "            print(f'{gene}: Not found in datasets')\n", "else:\n", "    print('ERROR: Datasets not loaded. Please run the data loading cell first.')\n", "\n", "# Test motif discovery classes\n", "try:\n", "    test_sequences = ['ATCGATCG', 'GCTAGCTA', 'TTAACCGG']\n", "    motif_discovery = MotifDiscovery(test_sequences)\n", "    test_motif = motif_discovery.generate_random_motif(6)\n", "    print(f'Generated test motif: {test_motif}')\n", "    print('Motif discovery classes working correctly')\n", "except Exception as e:\n", "    print(f'Error in motif discovery classes: {e}')\n", "\n", "print('\\nImplementation test completed!')\n"]}, {"cell_type": "markdown", "metadata": {"id": "main_execution"}, "source": ["## 5. Main Execution Loop - Processing Each Gene\\n\\nNow we'll process each gene (APP, PSEN1, PSEN2) sequentially with the complete pipeline."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "process_app_gene", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "92d5e31d-ae2a-4344-b762-233a0124b69a"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "================================================================================\n", "PROCESSING APP GENE\n", "================================================================================\n", "APP Healthy sequences: 7\n", "APP Unhealthy sequences: 6\n", "\n", "--- Processing motif length 6 ---\n", "\n", "=== Running Hybrid GA-PSO Optimization ===\n", "Step 1: Running Genetic Algorithm for exploration...\n", "Generation 0: Best score = 194.0000\n", "Generation 10: Best score = 349.0000\n", "Generation 20: Best score = 349.0000\n", "GA Results: Best motif = TATATA, Score = 349.0000\n", "\n", "Step 2: Running PSO with GA results as initial population...\n", "Error processing motif length 6: Cannot cast ufunc 'add' output from dtype('float64') to dtype('int64') with casting rule 'same_kind'\n", "\n", "--- Processing motif length 7 ---\n", "\n", "=== Running Hybrid GA-PSO Optimization ===\n", "Step 1: Running Genetic Algorithm for exploration...\n", "Generation 0: Best score = 49.0000\n", "Generation 10: Best score = 240.0000\n", "Generation 20: Best score = 240.0000\n", "GA Results: Best motif = ATATATA, Score = 240.0000\n", "\n", "Step 2: Running PSO with GA results as initial population...\n", "Error processing motif length 7: Cannot cast ufunc 'add' output from dtype('float64') to dtype('int64') with casting rule 'same_kind'\n", "\n", "--- Processing motif length 8 ---\n", "\n", "=== Running Hybrid GA-PSO Optimization ===\n", "Step 1: Running Genetic Algorithm for exploration...\n", "Generation 0: Best score = 64.0000\n", "Generation 10: Best score = 59.0000\n", "Generation 20: Best score = 61.0000\n", "GA Results: Best motif = ATATATAT, Score = 160.0000\n", "\n", "Step 2: Running PSO with GA results as initial population...\n", "Error processing motif length 8: Cannot cast ufunc 'add' output from dtype('float64') to dtype('int64') with casting rule 'same_kind'\n", "\n", "--- Processing motif length 9 ---\n", "\n", "=== Running Hybrid GA-PSO Optimization ===\n", "Step 1: Running Genetic Algorithm for exploration...\n", "Generation 0: Best score = 22.0000\n", "Generation 10: Best score = 21.0000\n", "Generation 20: Best score = 56.0000\n", "GA Results: Best motif = TTTTTCATT, Score = 56.0000\n", "\n", "Step 2: Running PSO with GA results as initial population...\n", "Error processing motif length 9: Cannot cast ufunc 'add' output from dtype('float64') to dtype('int64') with casting rule 'same_kind'\n", "\n", "--- Processing motif length 10 ---\n", "\n", "=== Running Hybrid GA-PSO Optimization ===\n", "Step 1: Running Genetic Algorithm for exploration...\n", "Generation 0: Best score = 8.0000\n", "Generation 10: Best score = 12.0000\n", "Generation 20: Best score = 12.0000\n", "GA Results: Best motif = AAAATAAAGA, Score = 13.0000\n", "\n", "Step 2: Running PSO with GA results as initial population...\n", "Error processing motif length 10: Cannot cast ufunc 'add' output from dtype('float64') to dtype('int64') with casting rule 'same_kind'\n", "\n", "APP gene processing completed!\n", "--------------------------------------------------\n"]}], "source": ["# Process APP Gene\n", "print('\\n' + '=' * 80)\n", "print('PROCESSING APP GENE')\n", "print('=' * 80)\n", "\n", "# Get APP sequences\n", "app_healthy = datasets['APP']['healthy']['sequences']\n", "app_unhealthy = datasets['APP']['unhealthy']['sequences']\n", "\n", "print(f'APP Healthy sequences: {len(app_healthy)}')\n", "print(f'APP Unhealthy sequences: {len(app_unhealthy)}')\n", "\n", "# Check if sequences exist\n", "if not app_healthy or not app_unhealthy:\n", "    print('ERROR: APP sequences not found. Please check data loading.')\n", "else:\n", "    # Run motif discovery for different motif lengths\n", "    app_results = {}\n", "    motif_lengths = [6, 7, 8, 9, 10]\n", "\n", "    for motif_length in motif_lengths:\n", "        print(f'\\n--- Processing motif length {motif_length} ---')\n", "\n", "        try:\n", "            # Initialize hybrid finder\n", "            hybrid_finder = HybridGAPSOMotifFinder(\n", "                target_sequences=app_unhealthy,  # Unhealthy as target\n", "                background_sequences=app_healthy,  # Healthy as background\n", "                motif_length=motif_length\n", "            )\n", "\n", "            # Run optimization\n", "            results = hybrid_finder.run_hybrid_optimization()\n", "            app_results[motif_length] = results\n", "\n", "            print(f'Best motif for length {motif_length}: {results[\"hybrid_best\"]} (Score: {results[\"hybrid_score\"]:.4f})')\n", "\n", "        except Exception as e:\n", "            print(f'Error processing motif length {motif_length}: {str(e)}')\n", "            continue\n", "\n", "    print('\\nAPP gene processing completed!')\n", "    print('-' * 50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "pwm_analysis", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "44878671-5f33-4482-c039-401d7f493d44"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["APP results not available. Please run the APP processing cell first.\n"]}], "source": ["# PWM Analysis and Comparison\n", "def create_pwm(motifs, pseudocount=0.1):\n", "    \"\"\"Create Position Weight Matrix from motifs.\"\"\"\n", "    if not motifs:\n", "        return None, None\n", "\n", "    motif_length = len(motifs[0])\n", "    nucleotides = ['A', 'T', 'G', 'C']\n", "\n", "    # Initialize count matrix\n", "    count_matrix = np.zeros((4, motif_length))\n", "\n", "    for motif in motifs:\n", "        for i, nucleotide in enumerate(motif):\n", "            if nucleotide in nucleotides:\n", "                count_matrix[nucleotides.index(nucleotide), i] += 1\n", "\n", "    # Add pseudocounts and normalize\n", "    count_matrix += pseudocount\n", "    pwm = count_matrix / np.sum(count_matrix, axis=0)\n", "\n", "    return pwm, nucleotides\n", "\n", "def visualize_pwm(pwm, nucleotides, title=\"Position Weight Matrix\"):\n", "    \"\"\"Visualize PWM as a logo plot.\"\"\"\n", "    # Create DataFrame for logomaker\n", "    pwm_df = pd.DataFrame(pwm.T, columns=nucleotides)\n", "\n", "    # Create logo plot\n", "    fig, ax = plt.subplots(figsize=(12, 4))\n", "    try:\n", "        logo = logomaker.Logo(pwm_df, ax=ax)\n", "        ax.set_title(title)\n", "        ax.set_xlabel('Position')\n", "        ax.set_ylabel('Probability')\n", "    except:\n", "        # Fallback to heatmap if logomaker fails\n", "        sns.heatmap(pwm, annot=True, xticklabels=range(1, len(pwm[0])+1),\n", "                   yticklabels=nucleotides, cmap='Blues', ax=ax)\n", "        ax.set_title(title)\n", "        ax.set_xlabel('Position')\n", "        ax.set_ylabel('Nucleotide')\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Compare PWMs for GA, PSO, and Hybrid results\n", "def compare_algorithms_pwm(results, gene_name, motif_length):\n", "    \"\"\"Compare PWMs from different algorithms.\"\"\"\n", "    print(f\"\\n=== PWM Comparison for {gene_name} (Length {motif_length}) ===\")\n", "\n", "    # Extract motifs from each algorithm\n", "    ga_motif = [results['ga']['motif']]\n", "    pso_motif = [results['pso']['motif']]\n", "    hybrid_motif = [results['hybrid_best']]\n", "\n", "    # Create and visualize PWMs\n", "    for name, motifs in [('GA', ga_motif), ('PSO', pso_motif), ('Hybrid', hybrid_motif)]:\n", "        pwm, nucleotides = create_pwm(motifs)\n", "        if pwm is not None:\n", "            visualize_pwm(pwm, nucleotides, f\"{name} - {gene_name} (Length {motif_length})\")\n", "\n", "# Apply PWM analysis to APP results\n", "if 'app_results' in globals() and app_results:\n", "    for motif_length in motif_lengths:\n", "        if motif_length in app_results:\n", "            compare_algorithms_pwm(app_results[motif_length], \"APP\", motif_length)\n", "else:\n", "    print('APP results not available. Please run the APP processing cell first.')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ml_validation", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "956faa7d-27b9-4944-cfa2-0c16c0ec7860"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["APP results not available. Please run the APP processing cell first.\n"]}], "source": ["# ML Feature Extraction and Validation\n", "def extract_motif_features(sequences, motifs):\n", "    \"\"\"Extract features based on motif occurrences.\"\"\"\n", "    features = []\n", "\n", "    for seq in sequences:\n", "        seq_features = []\n", "        for motif in motifs:\n", "            # Count occurrences\n", "            count = seq.count(motif)\n", "            # Frequency\n", "            frequency = count / len(seq) if len(seq) > 0 else 0\n", "            seq_features.extend([count, frequency])\n", "\n", "        # Additional sequence features\n", "        seq_features.extend([\n", "            len(seq),\n", "            GC(seq) if seq else 0,\n", "            seq.count('A') / len(seq) if len(seq) > 0 else 0,\n", "            seq.count('T') / len(seq) if len(seq) > 0 else 0,\n", "            seq.count('G') / len(seq) if len(seq) > 0 else 0,\n", "            seq.count('C') / len(seq) if len(seq) > 0 else 0\n", "        ])\n", "\n", "        features.append(seq_features)\n", "\n", "    return np.array(features)\n", "\n", "def validate_motifs_ml(healthy_sequences, unhealthy_sequences, discovered_motifs):\n", "    \"\"\"Validate discovered motifs using machine learning.\"\"\"\n", "    print(\"\\n=== Machine Learning Validation ===\")\n", "\n", "    # Extract features\n", "    healthy_features = extract_motif_features(healthy_sequences, discovered_motifs)\n", "    unhealthy_features = extract_motif_features(unhealthy_sequences, discovered_motifs)\n", "\n", "    # Create labels\n", "    healthy_labels = np.zeros(len(healthy_features))\n", "    unhealthy_labels = np.ones(len(unhealthy_features))\n", "\n", "    # Combine data\n", "    X = np.vstack([healthy_features, unhealthy_features])\n", "    y = np.hstack([healthy_labels, unhealthy_labels])\n", "\n", "    # Split data\n", "    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)\n", "\n", "    # Scale features\n", "    scaler = StandardScaler()\n", "    X_train_scaled = scaler.fit_transform(X_train)\n", "    X_test_scaled = scaler.transform(X_test)\n", "\n", "    # Train models\n", "    models = {\n", "        'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),\n", "        'XGBoost': xgb.XGBClassifier(random_state=42, eval_metric='logloss')\n", "    }\n", "\n", "    results = {}\n", "    for name, model in models.items():\n", "        # Train\n", "        model.fit(X_train_scaled, y_train)\n", "\n", "        # Predict\n", "        y_pred = model.predict(X_test_scaled)\n", "        y_pred_proba = model.predict_proba(X_test_scaled)[:, 1]\n", "\n", "        # Evaluate\n", "        auc_score = roc_auc_score(y_test, y_pred_proba)\n", "\n", "        results[name] = {\n", "            'model': model,\n", "            'auc': auc_score,\n", "            'predictions': y_pred,\n", "            'probabilities': y_pred_proba\n", "        }\n", "\n", "        print(f\"{name} AUC: {auc_score:.4f}\")\n", "        print(f\"{name} Classification Report:\")\n", "        print(classification_report(y_test, y_pred))\n", "        print(\"-\" * 50)\n", "\n", "    return results, X_test_scaled, y_test, scaler\n", "\n", "# Apply ML validation to APP results\n", "if 'app_results' in globals() and app_results:\n", "    app_best_motifs = [app_results[length]['hybrid_best'] for length in motif_lengths if length in app_results]\n", "    if app_best_motifs:\n", "        app_ml_results, app_X_test, app_y_test, app_scaler = validate_motifs_ml(\n", "            app_healthy, app_unhealthy, app_best_motifs\n", "        )\n", "    else:\n", "        print('No APP motifs found for ML validation.')\n", "else:\n", "    print('APP results not available. Please run the APP processing cell first.')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "explainability", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "d9d45f32-f9df-45e8-d25c-cf2beb0f4dad"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["APP ML results not available. Please run the ML validation cell first.\n"]}], "source": ["# SHAP and LIME Explainability\n", "def explain_predictions(model, X_test, feature_names, model_name=\"Model\"):\n", "    \"\"\"Generate explanations using SHAP and LIME.\"\"\"\n", "    print(f\"\\n=== Explainability Analysis for {model_name} ===\")\n", "\n", "    # SHAP Analysis\n", "    try:\n", "        if hasattr(model, 'feature_importances_'):\n", "            # For tree-based models\n", "            explainer = shap.<PERSON>Explainer(model)\n", "            shap_values = explainer.shap_values(X_test)\n", "\n", "            # Handle binary classification\n", "            if isinstance(shap_values, list):\n", "                shap_values = shap_values[1]  # Use positive class\n", "\n", "            # Summary plot\n", "            plt.figure(figsize=(10, 6))\n", "            shap.summary_plot(shap_values, X_test, feature_names=feature_names, show=False)\n", "            plt.title(f\"SHAP Summary - {model_name}\")\n", "            plt.tight_layout()\n", "            plt.show()\n", "\n", "            # Feature importance\n", "            feature_importance = np.abs(shap_values).mean(0)\n", "            importance_df = pd.DataFrame({\n", "                'feature': feature_names,\n", "                'importance': feature_importance\n", "            }).sort_values('importance', ascending=False)\n", "\n", "            print(f\"Top 10 Most Important Features ({model_name}):\")\n", "            print(importance_df.head(10))\n", "\n", "    except Exception as e:\n", "        print(f\"SHAP analysis failed: {e}\")\n", "\n", "    # LIME Analysis\n", "    try:\n", "        lime_explainer = LimeTabularExplainer(\n", "            X_test, feature_names=feature_names,\n", "            class_names=['Healthy', 'Unhealthy'], mode='classification'\n", "        )\n", "\n", "        # Explain a few instances\n", "        for i in range(min(3, len(X_test))):\n", "            explanation = lime_explainer.explain_instance(\n", "                X_test[i], model.predict_proba, num_features=10\n", "            )\n", "\n", "            print(f\"\\nLIME Explanation for Instance {i+1}:\")\n", "            for feature, weight in explanation.as_list():\n", "                print(f\"  {feature}: {weight:.4f}\")\n", "\n", "    except Exception as e:\n", "        print(f\"LIME analysis failed: {e}\")\n", "\n", "# Generate feature names\n", "def generate_feature_names(motifs):\n", "    \"\"\"Generate feature names for ML model.\"\"\"\n", "    feature_names = []\n", "\n", "    for motif in motifs:\n", "        feature_names.extend([f\"{motif}_count\", f\"{motif}_frequency\"])\n", "\n", "    feature_names.extend([\n", "        \"sequence_length\", \"gc_content\",\n", "        \"A_frequency\", \"T_frequency\", \"G_frequency\", \"C_frequency\"\n", "    ])\n", "\n", "    return feature_names\n", "\n", "# Apply explainability to APP results\n", "if 'app_ml_results' in globals() and 'app_best_motifs' in globals():\n", "    app_feature_names = generate_feature_names(app_best_motifs)\n", "\n", "    for model_name, model_data in app_ml_results.items():\n", "        explain_predictions(\n", "            model_data['model'], app_X_test,\n", "            app_feature_names, f\"APP {model_name}\"\n", "        )\n", "else:\n", "    print('APP ML results not available. Please run the ML validation cell first.')"]}, {"cell_type": "markdown", "metadata": {"id": "repeat_other_genes"}, "source": ["## 6. Repeat Analysis for PSEN1 and PSEN2 Genes\\n\\nNow repeat the same analysis pipeline for the other two genes."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "process_psen1_gene", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "a8677bb2-ae6a-4b72-bfb0-f1779d3b91a5"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "================================================================================\n", "PROCESSING PSEN1 GENE\n", "================================================================================\n", "PSEN1 Healthy sequences: 10\n", "PSEN1 Unhealthy sequences: 26\n", "\n", "--- Processing motif length 6 ---\n", "\n", "=== Running Hybrid GA-PSO Optimization ===\n", "Step 1: Running Genetic Algorithm for exploration...\n", "Generation 0: Best score = 0.0000\n", "Generation 10: Best score = 13.0000\n", "Generation 20: Best score = 13.0000\n", "GA Results: Best motif = CGACCC, Score = 13.0000\n", "\n", "Step 2: Running PSO with GA results as initial population...\n", "Error processing motif length 6: Cannot cast ufunc 'add' output from dtype('float64') to dtype('int64') with casting rule 'same_kind'\n", "\n", "--- Processing motif length 7 ---\n", "\n", "=== Running Hybrid GA-PSO Optimization ===\n", "Step 1: Running Genetic Algorithm for exploration...\n", "Generation 0: Best score = 13.0000\n", "Generation 10: Best score = 16.0000\n", "Generation 20: Best score = 21.0000\n", "GA Results: Best motif = TCATCGC, Score = 21.0000\n", "\n", "Step 2: Running PSO with GA results as initial population...\n", "Error processing motif length 7: Cannot cast ufunc 'add' output from dtype('float64') to dtype('int64') with casting rule 'same_kind'\n", "\n", "--- Processing motif length 8 ---\n", "\n", "=== Running Hybrid GA-PSO Optimization ===\n", "Step 1: Running Genetic Algorithm for exploration...\n", "Generation 0: Best score = 13.0000\n", "Generation 10: Best score = 10.0000\n", "Generation 20: Best score = 16.0000\n", "GA Results: Best motif = GATTCCCG, Score = 10.0000\n", "\n", "Step 2: Running PSO with GA results as initial population...\n", "Error processing motif length 8: Cannot cast ufunc 'add' output from dtype('float64') to dtype('int64') with casting rule 'same_kind'\n", "\n", "--- Processing motif length 9 ---\n", "\n", "=== Running Hybrid GA-PSO Optimization ===\n", "Step 1: Running Genetic Algorithm for exploration...\n", "Generation 0: Best score = 0.0000\n", "Generation 10: Best score = 8.0000\n", "Generation 20: Best score = 8.0000\n", "GA Results: Best motif = AGATGGTCC, Score = 8.0000\n", "\n", "Step 2: Running PSO with GA results as initial population...\n", "Error processing motif length 9: Cannot cast ufunc 'add' output from dtype('float64') to dtype('int64') with casting rule 'same_kind'\n", "\n", "--- Processing motif length 10 ---\n", "\n", "=== Running Hybrid GA-PSO Optimization ===\n", "Step 1: Running Genetic Algorithm for exploration...\n", "Generation 0: Best score = 24.0000\n", "Generation 10: Best score = 8.0000\n", "Generation 20: Best score = 0.0000\n", "GA Results: Best motif = GCGAATGCCT, Score = 0.0000\n", "\n", "Step 2: Running PSO with GA results as initial population...\n", "Error processing motif length 10: Cannot cast ufunc 'add' output from dtype('float64') to dtype('int64') with casting rule 'same_kind'\n", "\n", "PSEN1 gene processing completed!\n", "No PSEN1 results available for ML validation.\n"]}], "source": ["# Process PSEN1 Gene\n", "print('\\n' + '=' * 80)\n", "print('PROCESSING PSEN1 GENE')\n", "print('=' * 80)\n", "\n", "# Get PSEN1 sequences\n", "psen1_healthy = datasets['PSEN1']['healthy']['sequences']\n", "psen1_unhealthy = datasets['PSEN1']['unhealthy']['sequences']\n", "\n", "print(f'PSEN1 Healthy sequences: {len(psen1_healthy)}')\n", "print(f'PSEN1 Unhealthy sequences: {len(psen1_unhealthy)}')\n", "\n", "# Check if sequences exist\n", "if not psen1_healthy or not psen1_unhealthy:\n", "    print('ERROR: PSEN1 sequences not found. Please check data loading.')\n", "else:\n", "    # Run motif discovery for different motif lengths\n", "    psen1_results = {}\n", "\n", "    for motif_length in motif_lengths:\n", "        print(f'\\n--- Processing motif length {motif_length} ---')\n", "\n", "        try:\n", "            # Initialize hybrid finder\n", "            hybrid_finder = HybridGAPSOMotifFinder(\n", "                target_sequences=psen1_unhealthy,\n", "                background_sequences=psen1_healthy,\n", "                motif_length=motif_length\n", "            )\n", "\n", "            # Run optimization\n", "            results = hybrid_finder.run_hybrid_optimization()\n", "            psen1_results[motif_length] = results\n", "\n", "            print(f'Best motif for length {motif_length}: {results[\"hybrid_best\"]} (Score: {results[\"hybrid_score\"]:.4f})')\n", "\n", "        except Exception as e:\n", "            print(f'Error processing motif length {motif_length}: {str(e)}')\n", "            continue\n", "\n", "    print('\\nPSEN1 gene processing completed!')\n", "\n", "    # Apply ML validation to PSEN1 results\n", "    if psen1_results:\n", "        psen1_best_motifs = [psen1_results[length]['hybrid_best'] for length in motif_lengths if length in psen1_results]\n", "        if psen1_best_motifs:\n", "            psen1_ml_results, psen1_X_test, psen1_y_test, psen1_scaler = validate_motifs_ml(\n", "                psen1_healthy, psen1_unhealthy, psen1_best_motifs\n", "            )\n", "        else:\n", "            print('No PSEN1 motifs found for ML validation.')\n", "    else:\n", "        print('No PSEN1 results available for ML validation.')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "process_psen2_gene", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "06500621-276a-4aa1-dfd7-3ed2ca4df835"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "================================================================================\n", "PROCESSING PSEN2 GENE\n", "================================================================================\n", "PSEN2 Healthy sequences: 50\n", "PSEN2 Unhealthy sequences: 6\n", "\n", "--- Processing motif length 6 ---\n", "\n", "=== Running Hybrid GA-PSO Optimization ===\n", "Step 1: Running Genetic Algorithm for exploration...\n", "Generation 0: Best score = 0.0000\n", "Generation 10: Best score = 1.0000\n", "Generation 20: Best score = 1.0000\n", "GA Results: Best motif = TAGGCG, Score = 0.0000\n", "\n", "Step 2: Running PSO with GA results as initial population...\n", "Error processing motif length 6: Cannot cast ufunc 'add' output from dtype('float64') to dtype('int64') with casting rule 'same_kind'\n", "\n", "--- Processing motif length 7 ---\n", "\n", "=== Running Hybrid GA-PSO Optimization ===\n", "Step 1: Running Genetic Algorithm for exploration...\n", "Generation 0: Best score = 0.0000\n", "Generation 10: Best score = 0.0000\n", "Generation 20: Best score = 0.0000\n", "GA Results: Best motif = CGCGGGA, Score = 0.0000\n", "\n", "Step 2: Running PSO with GA results as initial population...\n", "Error processing motif length 7: Cannot cast ufunc 'add' output from dtype('float64') to dtype('int64') with casting rule 'same_kind'\n", "\n", "--- Processing motif length 8 ---\n", "\n", "=== Running Hybrid GA-PSO Optimization ===\n", "Step 1: Running Genetic Algorithm for exploration...\n", "Generation 0: Best score = 0.0000\n", "Generation 10: Best score = 0.0000\n", "Generation 20: Best score = 0.0000\n", "GA Results: Best motif = TTCGAGCG, Score = 0.0000\n", "\n", "Step 2: Running PSO with GA results as initial population...\n", "Error processing motif length 8: Cannot cast ufunc 'add' output from dtype('float64') to dtype('int64') with casting rule 'same_kind'\n", "\n", "--- Processing motif length 9 ---\n", "\n", "=== Running Hybrid GA-PSO Optimization ===\n", "Step 1: Running Genetic Algorithm for exploration...\n", "Generation 0: Best score = 0.0000\n", "Generation 10: Best score = 0.0000\n", "Generation 20: Best score = 0.0000\n", "GA Results: Best motif = ACCGTACTG, Score = 0.0000\n", "\n", "Step 2: Running PSO with GA results as initial population...\n", "Error processing motif length 9: Cannot cast ufunc 'add' output from dtype('float64') to dtype('int64') with casting rule 'same_kind'\n", "\n", "--- Processing motif length 10 ---\n", "\n", "=== Running Hybrid GA-PSO Optimization ===\n", "Step 1: Running Genetic Algorithm for exploration...\n", "Generation 0: Best score = 0.0000\n", "Generation 10: Best score = 0.0000\n", "Generation 20: Best score = 0.0000\n", "GA Results: Best motif = TCTTCCGTGT, Score = 0.0000\n", "\n", "Step 2: Running PSO with GA results as initial population...\n", "Error processing motif length 10: Cannot cast ufunc 'add' output from dtype('float64') to dtype('int64') with casting rule 'same_kind'\n", "\n", "PSEN2 gene processing completed!\n", "No PSEN2 results available for ML validation.\n"]}], "source": ["# Process PSEN2 Gene\n", "print('\\n' + '=' * 80)\n", "print('PROCESSING PSEN2 GENE')\n", "print('=' * 80)\n", "\n", "# Get PSEN2 sequences\n", "psen2_healthy = datasets['PSEN2']['healthy']['sequences']\n", "psen2_unhealthy = datasets['PSEN2']['unhealthy']['sequences']\n", "\n", "print(f'PSEN2 Healthy sequences: {len(psen2_healthy)}')\n", "print(f'PSEN2 Unhealthy sequences: {len(psen2_unhealthy)}')\n", "\n", "# Check if sequences exist\n", "if not psen2_healthy or not psen2_unhealthy:\n", "    print('ERROR: PSEN2 sequences not found. Please check data loading.')\n", "else:\n", "    # Run motif discovery for different motif lengths\n", "    psen2_results = {}\n", "\n", "    for motif_length in motif_lengths:\n", "        print(f'\\n--- Processing motif length {motif_length} ---')\n", "\n", "        try:\n", "            # Initialize hybrid finder\n", "            hybrid_finder = HybridGAPSOMotifFinder(\n", "                target_sequences=psen2_unhealthy,\n", "                background_sequences=psen2_healthy,\n", "                motif_length=motif_length\n", "            )\n", "\n", "            # Run optimization\n", "            results = hybrid_finder.run_hybrid_optimization()\n", "            psen2_results[motif_length] = results\n", "\n", "            print(f'Best motif for length {motif_length}: {results[\"hybrid_best\"]} (Score: {results[\"hybrid_score\"]:.4f})')\n", "\n", "        except Exception as e:\n", "            print(f'Error processing motif length {motif_length}: {str(e)}')\n", "            continue\n", "\n", "    print('\\nPSEN2 gene processing completed!')\n", "\n", "    # Apply ML validation to PSEN2 results\n", "    if psen2_results:\n", "        psen2_best_motifs = [psen2_results[length]['hybrid_best'] for length in motif_lengths if length in psen2_results]\n", "        if psen2_best_motifs:\n", "            psen2_ml_results, psen2_X_test, psen2_y_test, psen2_scaler = validate_motifs_ml(\n", "                psen2_healthy, psen2_unhealthy, psen2_best_motifs\n", "            )\n", "        else:\n", "            print('No PSEN2 motifs found for ML validation.')\n", "    else:\n", "        print('No PSEN2 results available for ML validation.')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "final_summary", "colab": {"base_uri": "https://localhost:8080/", "height": 176}, "outputId": "9e987a4a-75f4-434e-f4fb-c693a6569e55"}, "outputs": [{"output_type": "error", "ename": "SyntaxError", "evalue": "unexpected character after line continuation character (ipython-input-**********.py, line 5)", "traceback": ["\u001b[0;36m  File \u001b[0;32m\"/tmp/ipython-input-**********.py\"\u001b[0;36m, line \u001b[0;32m5\u001b[0m\n\u001b[0;31m    \\n    for gene, gene_results in all_results.items():\\n        for motif_length, results in gene_results.items():\\n            summary_data.append({\\n                'Gene': gene,\\n                'Motif_Length': motif_length,\\n                'GA_Motif': results['ga']['motif'],\\n                'GA_Score': results['ga']['score'],\\n                'PSO_Motif': results['pso']['motif'],\\n                'PSO_Score': results['pso']['score'],\\n                'Hybrid_Motif': results['hybrid_best'],\\n                'Hybrid_Score': results['hybrid_score']\\n            })\\n    \\n    return pd.DataFrame(summary_data)\\n\\n# Create final summary\\nall_results = {\\n    'APP': app_results,\\n    'PSEN1': psen1_results,\\n    'PSEN2': psen2_results\\n}\\n\\nfinal_summary = create_results_summary(all_results)\\nprint(\\\"\\\\n\\\" + \\\"=\\\" * 80)\\nprint(\\\"FINAL RESULTS SUMMARY\\\")\\nprint(\\\"=\\\" * 80)\\nprint(final_summary)\\n\\n# Save results\\nfinal_summary.to_csv('alzheimer_motif_discovery_results.csv', index=False)\\nprint(\\\"\\\\nResults saved to 'alzheimer_motif_discovery_results.csv'\\\")\u001b[0m\n\u001b[0m     ^\u001b[0m\n\u001b[0;31mSyntaxError\u001b[0m\u001b[0;31m:\u001b[0m unexpected character after line continuation character\n"]}], "source": ["# Comprehensive Results Summary\n", "def create_results_summary(all_results):\n", "    \"\"\"Create comprehensive summary of all results.\"\"\"\n", "    summary_data = []\n", "    \\n    for gene, gene_results in all_results.items():\\n        for motif_length, results in gene_results.items():\\n            summary_data.append({\\n                'Gene': gene,\\n                'Motif_Length': motif_length,\\n                'GA_Motif': results['ga']['motif'],\\n                'GA_Score': results['ga']['score'],\\n                'PSO_Motif': results['pso']['motif'],\\n                'PSO_Score': results['pso']['score'],\\n                'Hybrid_Motif': results['hybrid_best'],\\n                'Hybrid_Score': results['hybrid_score']\\n            })\\n    \\n    return pd.DataFrame(summary_data)\\n\\n# Create final summary\\nall_results = {\\n    'APP': app_results,\\n    'PSEN1': psen1_results,\\n    'PSEN2': psen2_results\\n}\\n\\nfinal_summary = create_results_summary(all_results)\\nprint(\\\"\\\\n\\\" + \\\"=\\\" * 80)\\nprint(\\\"FINAL RESULTS SUMMARY\\\")\\nprint(\\\"=\\\" * 80)\\nprint(final_summary)\\n\\n# Save results\\nfinal_summary.to_csv('alzheimer_motif_discovery_results.csv', index=False)\\nprint(\\\"\\\\nResults saved to 'alzheimer_motif_discovery_results.csv'\\\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "convergence_plots", "colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "outputId": "523c4e6b-4611-402d-82c7-fcc2793e9b4a"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1500x1200 with 6 Axes>"], "image/png": "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\n"}, "metadata": {}}], "source": ["# Plot convergence curves for all genes\n", "import matplotlib.pyplot as plt\n", "\n", "def plot_convergence_comparison():\n", "    \"\"\"Plot convergence curves for all algorithms and genes.\"\"\"\n", "    fig, axes = plt.subplots(3, 2, figsize=(15, 12))\n", "\n", "    genes = ['APP', 'PSEN1', 'PSEN2']\n", "    results_dict = {'APP': app_results, 'PSEN1': psen1_results, 'PSEN2': psen2_results}\n", "\n", "    for i, gene in enumerate(genes):\n", "        if gene not in results_dict:\n", "            print(f\"Skipping {gene}: results not available\")\n", "            continue\n", "\n", "        # Plot GA convergence\n", "        for motif_length in [6, 8, 10]:\n", "            if motif_length in results_dict[gene]:\n", "                ga_convergence = results_dict[gene][motif_length]['ga']['convergence']\n", "                axes[i, 0].plot(ga_convergence, label=f'Length {motif_length}')\n", "\n", "        axes[i, 0].set_title(f'{gene} - GA Convergence')\n", "        axes[i, 0].set_xlabel('Generation')\n", "        axes[i, 0].set_ylabel('Best Score')\n", "        axes[i, 0].legend()\n", "        axes[i, 0].grid(True)\n", "\n", "        # Plot PSO convergence\n", "        for motif_length in [6, 8, 10]:\n", "            if motif_length in results_dict[gene]:\n", "                pso_convergence = results_dict[gene][motif_length]['pso']['convergence']\n", "                axes[i, 1].plot(pso_convergence, label=f'Length {motif_length}')\n", "\n", "        axes[i, 1].set_title(f'{gene} - PSO Convergence')\n", "        axes[i, 1].set_xlabel('Iteration')\n", "        axes[i, 1].set_ylabel('Best Score')\n", "        axes[i, 1].legend()\n", "        axes[i, 1].grid(True)\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Call the plotting function\n", "plot_convergence_comparison()\n"]}, {"cell_type": "markdown", "metadata": {"id": "conclusions"}, "source": ["## 7. Conclusions and Next Steps\\n\\n### Key Findings:\\n1. **Hybrid GA-PSO Approach**: Successfully combines exploration (GA) and exploitation (PSO)\\n2. **Motif Discovery**: Identified potential Alzheimer's-associated motifs in APP, PSEN1, and PSEN2 genes\\n3. **ML Validation**: Machine learning models can distinguish between healthy and unhealthy sequences using discovered motifs\\n4. **Explainability**: SHAP and LIME provide insights into which motifs are most predictive\\n\\n### Recommendations:\\n1. **Biological Validation**: Validate discovered motifs through wet lab experiments\\n2. **Extended Analysis**: Include more genes and larger datasets\\n3. **Parameter Optimization**: Fine-tune GA and PSO parameters for better performance\\n4. **Clinical Correlation**: Correlate motifs with clinical Alzheimer's severity scores\\n\\n### Technical Achievements:\\n- ✅ Implemented hybrid metaheuristic optimization\\n- ✅ Processed multiple genes with different motif lengths\\n- ✅ Applied machine learning validation\\n- ✅ Provided explainable AI insights\\n- ✅ Generated comprehensive results and visualizations"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}