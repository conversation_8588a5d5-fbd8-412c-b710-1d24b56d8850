# Fixed PSO Implementation - Resolves Casting Error

class PSOMotifFinder:
    def __init__(self, target_sequences, background_sequences,
                 swarm_size=50, iterations=100, motif_length=8):
        self.target_sequences = target_sequences
        self.background_sequences = background_sequences
        self.swarm_size = swarm_size
        self.iterations = iterations
        self.motif_length = motif_length
        self.motif_discovery = MotifDiscovery(target_sequences)
        self.nucleotides = ['A', 'T', 'G', 'C']
        self.best_scores = []
        self.avg_scores = []

    def motif_to_vector(self, motif):
        """Convert motif string to numerical vector."""
        vector = []
        for nucleotide in motif:
            if nucleotide == 'A':
                vector.extend([1, 0, 0, 0])
            elif nucleotide == 'T':
                vector.extend([0, 1, 0, 0])
            elif nucleotide == 'G':
                vector.extend([0, 0, 1, 0])
            elif nucleotide == 'C':
                vector.extend([0, 0, 0, 1])
            else:
                vector.extend([0.25, 0.25, 0.25, 0.25])  # Unknown nucleotide
        return np.array(vector, dtype=np.float64)  # FIXED: Ensure float64 dtype

    def vector_to_motif(self, vector):
        """Convert numerical vector back to motif string."""
        motif = ""
        # Reshape vector to (motif_length, 4) matrix
        vector_matrix = vector.reshape(self.motif_length, 4)
        
        for position in vector_matrix:
            # Find the nucleotide with highest probability
            max_idx = np.argmax(position)
            motif += self.nucleotides[max_idx]
        
        return motif

    def fitness_function(self, vector):
        """Fitness function for PSO."""
        motif = self.vector_to_motif(vector)
        return self.motif_discovery.calculate_motif_score(
            motif, self.target_sequences, self.background_sequences)

    def optimize(self, initial_motifs=None):
        """Run PSO optimization with fixed casting."""
        print(f"Initializing PSO with swarm size {self.swarm_size}...")
        
        # Initialize swarm - FIXED: Ensure all arrays are float64
        if initial_motifs:
            particles = [self.motif_to_vector(m) for m in initial_motifs[:self.swarm_size]]
            while len(particles) < self.swarm_size:
                random_motif = self.motif_discovery.generate_random_motif(self.motif_length)
                particles.append(self.motif_to_vector(random_motif))
        else:
            particles = [self.motif_to_vector(
                self.motif_discovery.generate_random_motif(self.motif_length))
                for _ in range(self.swarm_size)]

        # FIXED: Ensure particles array is float64
        particles = np.array(particles, dtype=np.float64)
        velocities = np.random.uniform(-0.1, 0.1, particles.shape).astype(np.float64)

        # Initialize personal and global bests
        personal_best_positions = particles.copy()
        personal_best_scores = np.array([self.fitness_function(p) for p in particles])

        global_best_idx = np.argmax(personal_best_scores)
        global_best_position = personal_best_positions[global_best_idx].copy()
        global_best_score = personal_best_scores[global_best_idx]

        # PSO parameters
        w = 0.7   # Inertia weight
        c1 = 1.5  # Cognitive parameter
        c2 = 1.5  # Social parameter

        print(f"Starting PSO optimization for {self.iterations} iterations...")
        
        for iteration in range(self.iterations):
            for i in range(self.swarm_size):
                r1, r2 = np.random.random(2)
                
                # FIXED: Velocity update with proper float operations
                velocities[i] = (w * velocities[i] +
                                c1 * r1 * (personal_best_positions[i] - particles[i]) +
                                c2 * r2 * (global_best_position - particles[i]))
                
                # FIXED: Position update - both arrays are now float64
                particles[i] = particles[i] + velocities[i]
                
                # Clamp values to valid range [0, 1]
                particles[i] = np.clip(particles[i], 0.0, 1.0)

                fitness = self.fitness_function(particles[i])

                if fitness > personal_best_scores[i]:
                    personal_best_scores[i] = fitness
                    personal_best_positions[i] = particles[i].copy()

                if fitness > global_best_score:
                    global_best_score = fitness
                    global_best_position = particles[i].copy()

            # Track statistics
            current_scores = [self.fitness_function(p) for p in particles]
            self.best_scores.append(max(current_scores))
            self.avg_scores.append(np.mean(current_scores))

            if iteration % 20 == 0:
                print(f"Iteration {iteration}: Best score = {global_best_score:.4f}")

        best_motif = self.vector_to_motif(global_best_position)
        return best_motif, global_best_score, [self.vector_to_motif(p) for p in particles]

print("Fixed PSO implementation ready!")
