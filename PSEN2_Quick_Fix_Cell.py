# PSEN2 Quick Fix - Enhanced Motif Discovery
# Copy this entire cell into your Colab notebook

import numpy as np
import random

class EnhancedMotifDiscovery:
    """Enhanced motif discovery specifically for PSEN2 data."""
    
    def __init__(self, target_sequences):
        self.target_sequences = target_sequences
        self.nucleotides = ['A', 'T', 'G', 'C']
    
    def calculate_enhanced_motif_score(self, motif, target_sequences, background_sequences):
        """Enhanced scoring function that handles edge cases better."""
        if not motif or not target_sequences or not background_sequences:
            return 0.0
        
        # Remove empty sequences
        target_seqs = [seq for seq in target_sequences if seq and len(seq) >= len(motif)]
        background_seqs = [seq for seq in background_sequences if seq and len(seq) >= len(motif)]
        
        if not target_seqs or not background_seqs:
            return 0.0
        
        # Count occurrences with case-insensitive matching
        target_count = 0
        background_count = 0
        
        motif_upper = motif.upper()
        
        # Count in target sequences (unhealthy)
        for seq in target_seqs:
            seq_upper = seq.upper()
            for i in range(len(seq_upper) - len(motif_upper) + 1):
                if seq_upper[i:i+len(motif_upper)] == motif_upper:
                    target_count += 1
        
        # Count in background sequences (healthy)
        for seq in background_seqs:
            seq_upper = seq.upper()
            for i in range(len(seq_upper) - len(motif_upper) + 1):
                if seq_upper[i:i+len(motif_upper)] == motif_upper:
                    background_count += 1
        
        # Calculate total possible positions
        target_positions = sum(len(seq) - len(motif) + 1 for seq in target_seqs)
        background_positions = sum(len(seq) - len(motif) + 1 for seq in background_seqs)
        
        if target_positions == 0 or background_positions == 0:
            return 0.0
        
        # Calculate frequencies
        target_freq = target_count / target_positions
        background_freq = background_count / background_positions
        
        # Enhanced scoring with pseudocounts
        pseudocount = 1e-6
        
        # Log-odds ratio
        if background_freq == 0:
            if target_freq > 0:
                score = target_count * 100  # High score for motifs unique to target
            else:
                score = 0
        else:
            log_odds = np.log2((target_freq + pseudocount) / (background_freq + pseudocount))
            score = log_odds * (target_count + background_count)
        
        # Ensure non-negative score
        return max(0, score)
    
    def generate_random_motif(self, length):
        """Generate a random motif of specified length."""
        return ''.join(random.choices(self.nucleotides, k=length))

def run_enhanced_psen2_analysis():
    """Run enhanced PSEN2 motif discovery with better parameters."""
    
    print("🔧 Running Enhanced PSEN2 Motif Discovery...")
    print("=" * 60)
    
    # Create enhanced motif discovery instance
    enhanced_discovery = EnhancedMotifDiscovery(psen2_unhealthy)
    
    # Test with shorter motif lengths (better for PSEN2)
    motif_lengths = [4, 5, 6, 7, 8]
    
    enhanced_results = {}
    
    for length in motif_lengths:
        print(f"\n--- Testing motif length {length} ---")
        
        best_motif = None
        best_score = 0
        
        # Test more random motifs for better coverage
        num_tests = 1000
        
        for _ in range(num_tests):
            test_motif = enhanced_discovery.generate_random_motif(length)
            score = enhanced_discovery.calculate_enhanced_motif_score(
                test_motif, psen2_unhealthy, psen2_healthy
            )
            
            if score > best_score:
                best_score = score
                best_motif = test_motif
        
        enhanced_results[length] = {
            'motif': best_motif,
            'score': best_score
        }
        
        print(f"Best motif: {best_motif} (Score: {best_score:.4f})")
    
    # Also test some biologically relevant patterns
    print(f"\n--- Testing Biologically Relevant Patterns ---")
    
    bio_patterns = [
        'TATA', 'CAAT', 'GGGG', 'CCCC',  # Promoter elements
        'ATCG', 'CGAT', 'TACG', 'GCAT',  # Mixed patterns
        'ATAT', 'CGCG', 'TGCA', 'ACGT',  # Palindromes
        'AAAA', 'TTTT', 'GGGG', 'CCCC',  # Homopolymers
    ]
    
    bio_results = []
    
    for pattern in bio_patterns:
        score = enhanced_discovery.calculate_enhanced_motif_score(
            pattern, psen2_unhealthy, psen2_healthy
        )
        bio_results.append((pattern, score))
        print(f"{pattern}: {score:.4f}")
    
    # Sort biological patterns by score
    bio_results.sort(key=lambda x: x[1], reverse=True)
    
    print(f"\n--- ENHANCED PSEN2 RESULTS SUMMARY ---")
    print("Random motif discovery:")
    for length, result in enhanced_results.items():
        print(f"  Length {length}: {result['motif']} (Score: {result['score']:.4f})")
    
    print(f"\nTop biological patterns:")
    for pattern, score in bio_results[:5]:
        print(f"  {pattern}: {score:.4f}")
    
    # Compare with original results
    print(f"\n--- COMPARISON WITH ORIGINAL RESULTS ---")
    original_motifs = ['TAGGCG', 'CGCGGGA', 'TTCGAGCG', 'ACCGTACTG', 'TCTTCCGTGT']
    
    print("Original motif scores with enhanced scoring:")
    for motif in original_motifs:
        score = enhanced_discovery.calculate_enhanced_motif_score(
            motif, psen2_unhealthy, psen2_healthy
        )
        print(f"  {motif}: {score:.4f}")
    
    print("\n" + "=" * 60)
    print("ENHANCED PSEN2 ANALYSIS COMPLETE")
    print("=" * 60)
    
    return enhanced_results, bio_results

# Run the enhanced analysis
enhanced_psen2_results, bio_patterns_results = run_enhanced_psen2_analysis()
