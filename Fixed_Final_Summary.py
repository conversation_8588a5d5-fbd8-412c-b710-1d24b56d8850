# Fixed Final Summary Section - Resolves Syntax Errors

# Comprehensive Results Summary
def create_results_summary(all_results):
    """Create comprehensive summary of all results."""
    summary_data = []
    
    for gene, gene_results in all_results.items():
        for motif_length, results in gene_results.items():
            summary_data.append({
                'Gene': gene,
                'Motif_Length': motif_length,
                'GA_Motif': results['ga']['motif'],
                'GA_Score': results['ga']['score'],
                'PSO_Motif': results['pso']['motif'],
                'PSO_Score': results['pso']['score'],
                'Hybrid_Motif': results['hybrid_best'],
                'Hybrid_Score': results['hybrid_score']
            })
    
    return pd.DataFrame(summary_data)

# Create final summary with error handling
try:
    all_results = {}
    
    # Collect available results
    if 'app_results' in globals() and app_results:
        all_results['APP'] = app_results
        print(f"APP results collected: {len(app_results)} motif lengths")
    
    if 'psen1_results' in globals() and psen1_results:
        all_results['PSEN1'] = psen1_results
        print(f"PSEN1 results collected: {len(psen1_results)} motif lengths")
    
    if 'psen2_results' in globals() and psen2_results:
        all_results['PSEN2'] = psen2_results
        print(f"PSEN2 results collected: {len(psen2_results)} motif lengths")
    
    if all_results:
        final_summary = create_results_summary(all_results)
        print('\n' + '=' * 80)
        print('FINAL RESULTS SUMMARY')
        print('=' * 80)
        print(final_summary)
        
        # Save results
        final_summary.to_csv('alzheimer_motif_discovery_results.csv', index=False)
        print('\nResults saved to alzheimer_motif_discovery_results.csv')
        
        # Display summary statistics
        print('\n' + '=' * 50)
        print('SUMMARY STATISTICS')
        print('=' * 50)
        
        for gene in all_results.keys():
            gene_data = final_summary[final_summary['Gene'] == gene]
            if not gene_data.empty:
                best_ga = gene_data.loc[gene_data['GA_Score'].idxmax()]
                best_pso = gene_data.loc[gene_data['PSO_Score'].idxmax()]
                best_hybrid = gene_data.loc[gene_data['Hybrid_Score'].idxmax()]
                
                print(f'\n{gene} Gene:')
                print(f'  Best GA: {best_ga["GA_Motif"]} (Score: {best_ga["GA_Score"]:.4f})')
                print(f'  Best PSO: {best_pso["PSO_Motif"]} (Score: {best_pso["PSO_Score"]:.4f})')
                print(f'  Best Hybrid: {best_hybrid["Hybrid_Motif"]} (Score: {best_hybrid["Hybrid_Score"]:.4f})')
    else:
        print('No results available for summary.')
        print('This may be due to PSO casting errors preventing hybrid completion.')
        print('Please run the fixed PSO implementation.')

except Exception as e:
    print(f'Error creating summary: {e}')
    print('This is likely due to incomplete results from PSO errors.')

# Alternative summary for partial results
print('\n' + '=' * 50)
print('ALTERNATIVE SUMMARY (GA ONLY)')
print('=' * 50)

# Show GA results even if PSO failed
for gene_name in ['APP', 'PSEN1', 'PSEN2']:
    results_var = f'{gene_name.lower()}_results'
    if results_var in globals():
        results = globals()[results_var]
        if results:
            print(f'\n{gene_name} Gene GA Results:')
            for length, result in results.items():
                if 'ga' in result:
                    ga_motif = result['ga']['motif']
                    ga_score = result['ga']['score']
                    print(f'  Length {length}: {ga_motif} (Score: {ga_score:.4f})')

print('\nSummary completed!')
